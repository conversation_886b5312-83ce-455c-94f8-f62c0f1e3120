import { useGLTF } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import { Asset } from 'expo-asset';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Color, MeshStandardMaterial } from 'three';
import { ColorCombination } from '../services/colorService';

interface CasualWearModelProps {
    colorCombination?: ColorCombination;
    rotationY?: number;
    position?: [number, number, number];
    zoom?: number;
}

export function CasualWearModel({
    colorCombination,
    rotationY = 6.11, // Start with initial rotation showing front face
    position = [0, -0.8, 0], // Centered position for mobile
    zoom = 0.8 // Increased zoom for better mobile viewing
}: CasualWearModelProps) {
    const [casualModelUri, setCasualModelUri] = useState<string | null>(null);
    const [casualMaterialsReady, setCasualMaterialsReady] = useState(false);
    const casualGroupRef = useRef<any>(null);
    const lastCasualColorRef = useRef<string>('');
    const isUpdatingCasualColorsRef = useRef(false);

    // Load casualwear2.glb model with proper asset handling
    useEffect(() => {
        const loadCasualWearModel = async () => {
            try {
console.log('🚀 CasualWear: Loading model...');

                // Use Asset.fromModule for proper bundling
                const casualAsset = Asset.fromModule(require('../assets/models/casualwear.glb'));
                await casualAsset.downloadAsync();

                if (casualAsset.localUri || casualAsset.uri) {
                    const casualUri = casualAsset.localUri || casualAsset.uri;
                    setCasualModelUri(casualUri);
console.log('✅ CasualWear: Model loaded successfully');
                } else {
console.error('❌ CasualWear: No URI available from asset');
                }
            } catch (error) {
console.error('❌ CasualWear: Model loading failed:', error);
                // Fallback: try direct require as last resort
                try {
                    const fallbackUri = require('../assets/models/casualwear.glb');
                    setCasualModelUri(fallbackUri);
console.log('✅ CasualWear: Model loaded via fallback');
                } catch (fallbackError) {
console.error('❌ CasualWear: Fallback loading also failed:', fallbackError);
                }
            }
        };

        loadCasualWearModel();
    }, []);

    const casualGltf = casualModelUri ? useGLTF(casualModelUri) : null;

    // Setup CasualWear meshes and materials
    useEffect(() => {
        if (!casualGltf || !casualGltf.scene || casualMaterialsReady) return;

console.log('🎯 CasualWear: Setting up meshes and materials...');

        // Ensure all meshes have proper materials for color changes
        casualGltf.scene.traverse((child: any) => {
            if (child.isMesh) {
                // Ensure material exists and can be colored
                if (!child.material) {
                    child.material = new MeshStandardMaterial({
                        color: '#ffffff',
                        roughness: 0.7,
                        metalness: 0.1
                    });
                }

                // Disable shadows for performance
                child.castShadow = false;
                child.receiveShadow = false;
                child.frustumCulled = true;
            }
        });

        setCasualMaterialsReady(true);
console.log(`✅ CasualWear: Materials setup complete`);
    }, [casualGltf]);

    // Enhanced color application system for CasualWear
    const applyCasualWearColors = useCallback((colors: ColorCombination) => {
        if (!casualGltf || !casualGltf.scene) return;
        if (isUpdatingCasualColorsRef.current) return;

        const casualColorKey = `${colors?.shirt}-${colors?.pants}-${colors?.shoes}`;
        if (lastCasualColorRef.current === casualColorKey) return;

        isUpdatingCasualColorsRef.current = true;
        lastCasualColorRef.current = casualColorKey;

console.log('🎨 CasualWear: Applying colors:', colors);

        const casualColorMap = {
            skin: new Color('#fdbcb4'),
            hair: new Color('#000000'),
            shirt: new Color(colors.shirt || '#4a90e2'),
            pants: new Color(colors.pants || '#2c3e50'),
            shoes: new Color(colors.shoes || '#8b4513'),
            accessories: new Color(colors.shirt || '#4a90e2'),
            clothing: new Color(colors.shirt || '#4a90e2'),
            belt: new Color('#000000') // Black belt
        };

        let updatedMeshes = 0;

        // Traverse the entire scene to find and color meshes
        casualGltf.scene.traverse((child: any) => {
            if (child.isMesh && child.name) {
                const meshName = child.name.toLowerCase();
                let targetColor = casualColorMap.clothing; // default

                // Determine color based on mesh name
                if (meshName === 'face' || meshName === 'hand' || meshName === 'hand001' ||
                    meshName === 'leg' || meshName === 'leg001') {
                    targetColor = casualColorMap.skin;
                } else if (meshName === 'hair') {
                    targetColor = casualColorMap.hair;
                } else if (meshName === 'jacket' || meshName === 'underjacket' || meshName === 'neckcloth' ||
                           meshName === 'jackenthand' || meshName === 'jackethand') {
                    targetColor = casualColorMap.shirt;
                } else if (meshName === 'pant' || meshName === 'pant001') {
                    targetColor = casualColorMap.pants;
                } else if (meshName.includes('boot')) {
                    targetColor = casualColorMap.shoes;
                } else if (meshName === 'belt') {
                    targetColor = casualColorMap.belt; // Black belt
                } else if (meshName.includes('bag') || meshName === 'baghand') {
                    targetColor = casualColorMap.accessories;
                }

                // Apply color to mesh material
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach((mat: any) => {
                            if (mat.color) {
                                mat.color.copy(targetColor);
                                mat.needsUpdate = true;
                            }
                        });
                    } else if (child.material.color) {
                        child.material.color.copy(targetColor);
                        child.material.needsUpdate = true;
                    }
                    updatedMeshes++;
                }
            }
        });

console.log(`✅ CasualWear: Updated ${updatedMeshes} meshes with new colors`);
        isUpdatingCasualColorsRef.current = false;
    }, [casualGltf]);

    // Apply colors when colorCombination changes
    useEffect(() => {
        if (colorCombination && casualMaterialsReady) {
            applyCasualWearColors(colorCombination);
        }
    }, [colorCombination, casualMaterialsReady, applyCasualWearColors]);

    // Rotation system for CasualWear
    useFrame(() => {
        if (casualGroupRef.current) {
            casualGroupRef.current.rotation.y = rotationY;
            casualGroupRef.current.rotation.x = 0;
            casualGroupRef.current.rotation.z = 0;
        }
    });

    // Return null if model not ready
    if (!casualGltf || !casualGltf.scene || !casualMaterialsReady) {
        return null;
    }

    // Fixed positioning and rotation for mobile - Make model stand upright and centered
    return (
        <group
            ref={casualGroupRef}
            position={[position[0], position[1], position[2]]}
            scale={[zoom * 1.5, zoom * 1.5, zoom * 1.5]} // Proper scale for mobile
            rotation={[0, 0, 0]} // Group rotation handled by useFrame
        >
            <primitive
                object={casualGltf.scene}
                position={[0, 0, 0]}
                scale={[1, 1, 1]}
                rotation={[Math.PI / 2, 0, 0]} // Rotate +90° on X-axis to stand upright with head up
            />
        </group>
    );
}

// Preload CasualWear model
useGLTF.preload('../assets/models/casualwear.glb');
