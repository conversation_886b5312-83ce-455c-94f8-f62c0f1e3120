import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Animated,
    Dimensions,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { useRouter } from 'expo-router';
import { AuthWrapper } from '../../components/auth/AuthWrapper';
import { ColorRecommendationModal } from '../../components/ColorRecommendationModal';
import { useAuth } from '../../contexts/AuthContext';
import { useColor } from '../../contexts/ColorContext';
import { apiService, ColorRecommendation, FaceAnalysis } from '../../services/api';
import { ColorCombination, ColorService } from '../../services/colorService';

const { width, height } = Dimensions.get('window');

// Create a separate component for authenticated users
function AuthenticatedHomeScreen() {
  const { user, logout, refreshProfile } = useAuth();
  const { setColorCombinations, selectedCombination, setSelectedCombination, setUserColors } = useColor();
  const router = useRouter();

  // Initialize notification system (will be added after imports are fixed)
  // const notifications = useNotifications();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<FaceAnalysis | null>(null);
  const [colorRecommendations, setColorRecommendations] = useState<ColorRecommendation | null>(null);
  const [colorCombinations, setLocalColorCombinations] = useState<ColorCombination[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const [recommendationSource, setRecommendationSource] = useState<'gemini' | 'default' | null>(null);
  const scaleAnim = new Animated.Value(1);

  // Modal state for color recommendation details
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedOutfit, setSelectedOutfit] = useState<any>(null);

  // Check permissions on component mount
  useEffect(() => {
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    try {
      const { status } = await MediaLibrary.getPermissionsAsync();
      setHasPermission(status === 'granted');
    } catch (error) {
console.log('Permission check error:', error);
      setHasPermission(false);
    }
  };

  const requestPermission = async () => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      setHasPermission(status === 'granted');

      if (status === 'granted') {
        Alert.alert(
          'Permission Granted! 🎉',
          'You can now upload photos from your gallery.',
          [{ text: 'Great!', style: 'default' }]
        );
      } else {
        Alert.alert(
          'Permission Denied',
          'You can still take photos with the camera, but gallery access is limited.',
          [{ text: 'OK', style: 'default' }]
        );
      }

      return status === 'granted';
    } catch (error) {
console.log('Permission request error:', error);
      Alert.alert('Error', 'Failed to request permission. Please try again.');
      return false;
    }
  };

  const pickImageFromGallery = async () => {
    try {
      setIsLoading(true);

      // Check if we have permission, if not request it
      if (hasPermission === false) {
        const granted = await requestPermission();
        if (!granted) {
          setIsLoading(false);
          return;
        }
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
        setAnalysisResult(null); // Reset previous analysis
        animateButton();
      }
    } catch (error) {
console.log('Gallery picker error:', error);
      Alert.alert('Error', 'Failed to pick image from gallery.');
    } finally {
      setIsLoading(false);
    }
  };

  const takePhoto = async () => {
    try {
      setIsLoading(true);

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please allow camera access to take photos.',
          [{ text: 'OK', style: 'default' }]
        );
        setIsLoading(false);
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
        setAnalysisResult(null); // Reset previous analysis
        animateButton();
      }
    } catch (error) {
console.log('Camera error:', error);
      Alert.alert('Error', 'Failed to take photo.');
    } finally {
      setIsLoading(false);
    }
  };

  const animateButton = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const showImageOptions = () => {
    Alert.alert(
      'Select Image',
      'Choose how you want to add your photo',
      [
        {
          text: 'Camera',
          onPress: takePhoto,
          style: 'default',
        },
        {
          text: 'Gallery',
          onPress: pickImageFromGallery,
          style: 'default',
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ],
      { cancelable: true }
    );
  };

  const removeImage = () => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => {
            setSelectedImage(null);
            setAnalysisResult(null);
          },
          style: 'destructive',
        },
      ]
    );
  };

  const analyzeFace = async () => {
    if (!selectedImage) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    try {
      setIsAnalyzing(true);
console.log('🔍 Starting face analysis...');

      // First check if the service is available
console.log('🔍 Checking face analysis service health...');
      const serviceAvailable = await apiService.checkFaceAnalysisService();

      if (!serviceAvailable) {
        Alert.alert(
          'Service Unavailable',
          'The face analysis service is currently unavailable. Please try again later.',
          [{ text: 'OK' }]
        );
        return;
      }

console.log('✅ Service is available, proceeding with analysis...');
      const result = await apiService.analyzeFace(selectedImage);
console.log('✅ Face analysis result:', result);
console.log('🎨 Face analysis colors:', JSON.stringify(result.data?.colors, null, 2));

      // Check if face was actually detected BEFORE setting analysis result
      if (!result.success || !result.data?.faceDetected) {
console.log('No face detected in the image');
        Alert.alert(
          'No Face Detected',
          'No face was detected in the uploaded image. Please upload a clear front-facing photo with good lighting for color analysis.',
          [{ text: 'OK' }]
        );
        // Clear any previous analysis data and don't set new one
        setAnalysisResult(null);
        setColorRecommendations(null);
        setUserColors(null);
        setLocalColorCombinations([]);
        setColorCombinations([]);
        return; // Exit early, don't proceed with color recommendations
      }

      // Validate analysis result structure
      if (!result.data) {
console.error('Invalid analysis result: missing data');
        Alert.alert('Analysis Error', 'Invalid analysis result received. Please try again.');
        return;
      }

      // Extract analysis ID from backend response (backend returns _id)
      const analysisId = result.data._id || result.data.analysisId || result._id;

      // Ensure analysisId is available for color recommendations
      if (analysisId) {
        result.data.analysisId = analysisId; // Normalize to analysisId for consistency
console.log('✅ Analysis ID found and normalized:', analysisId);
      } else {
console.warn('⚠️ FRONTEND: No analysis ID found in response');
console.warn('⚠️ FRONTEND: Available keys:', Object.keys(result.data));
      }

      // Only set analysis result if face was detected and data is valid
      setAnalysisResult(result);
console.log('✅ Analysis result set successfully:', {
        hasColors: !!result.data.colors,
        hasFeatures: !!result.data.features,
        hasDimensions: !!result.data.dimensions,
        analysisId: result.data.analysisId
      });

      // Set user colors for dynamic model rendering (only if face detected)
      if (result.data?.colors) {
console.log('Setting user colors for dynamic model rendering');
        setUserColors(result.data.colors);
      }

      // Get color recommendations - handle both Gemini AI and default fallback
      await handleColorRecommendations(result);

      // Only show success alert if face was detected and processed
      Alert.alert(
        'Analysis Complete! 🎉',
        'Your face has been analyzed. Check out the color recommendations!',
        [
          {
            text: 'View on 3D Model',
            onPress: () => {
              // Navigate to appropriate model based on user gender
              // console.log('Alert Navigation: User gender is:', user?.gender); // REMOVED: Sensitive gender info
              if (user?.gender === 'male') {
                // console.log('Alert Navigation: Redirecting to male model (explore)'); // REMOVED: Sensitive navigation info
                router.push('/explore');
              } else if (user?.gender === 'female') {
                // console.log('Alert Navigation: Redirecting to female model'); // REMOVED: Sensitive navigation info
                router.push('/female');
              } else {
console.warn('Alert Navigation: Unknown gender, defaulting to male model');
                router.push('/explore');
              }
            },
          },
          { text: 'Stay Here', style: 'cancel' },
        ]
      );
    } catch (error: any) {
console.error('❌ Face analysis error:', error);

      // Handle specific error types
      if (error.message?.includes('Not authorized') || error.message?.includes('401') || error.message?.includes('Authentication failed')) {
        Alert.alert(
          'Session Expired',
          'Your session has expired. Please log out and log back in to continue.',
          [
            {
              text: 'Log Out',
              onPress: handleLogout,
              style: 'destructive'
            },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      } else if (error.message?.includes('Network connection failed') || error.message?.includes('Network request failed')) {
        Alert.alert(
          'Connection Error',
          'Please check your internet connection and try again.',
          [
            { text: 'Retry', onPress: analyzeFace },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      } else if (error.message?.includes('Backend service error') || error.message?.includes('validationResult is not defined')) {
        Alert.alert(
          'Service Temporarily Unavailable',
          'The face analysis service is experiencing technical difficulties. Our team has been notified and is working to resolve this issue.',
          [
            { text: 'Try Again Later', onPress: analyzeFace },
            { text: 'OK', style: 'cancel' }
          ]
        );
      } else if (error.message?.includes('Image upload service not configured') || error.message?.includes('Service Unavailable')) {
        Alert.alert(
          'Service Temporarily Unavailable',
          'The face analysis service is temporarily unavailable. Please try again in a few minutes.',
          [
            { text: 'Retry Later', onPress: analyzeFace },
            { text: 'OK', style: 'cancel' }
          ]
        );
      } else if (error.message?.includes('Image upload incomplete') || error.message?.includes('Image upload failed')) {
        Alert.alert(
          'Upload Error',
          'There was an issue uploading your image. Please try selecting and uploading the image again.',
          [
            { text: 'Try Again', onPress: () => {
              setSelectedImage(null); // Clear current image
              pickImage(); // Trigger image picker again
            }},
            { text: 'OK', style: 'cancel' }
          ]
        );
      } else {
        Alert.alert(
          'Analysis Failed',
          error.message || 'Failed to analyze face. Please try again with a clear front-facing photo.',
          [
            { text: 'Try Again', onPress: analyzeFace },
            { text: 'OK', style: 'cancel' }
          ]
        );
      }
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleLogout = () => {
    // Prevent multiple logout attempts
    if (isLoading) {
console.log('HomeScreen: ⚠️ Logout already in progress, ignoring');
      return;
    }

    Alert.alert(
      'Logout Confirmation',
      '🚪 Logout karna hai? Confirm kar de! 👋\n\n(Funny message will show for longer time now! 😄)',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          onPress: async () => {
            try {
console.log('HomeScreen: 🚪 User confirmed logout');

              // Show loading state to prevent double-clicks
              setIsLoading(true);

              await logout();
console.log('HomeScreen: ✅ Logout completed');

              // Show funny logout message with longer duration
              Alert.alert(
                'Logout Successful! 👋',
                '🎉 Logout ho gaya! Phir milenge jaldi! See you soon! 🚀',
                [{ text: 'OK', style: 'default' }]
              );

            } catch (error) {
console.error('HomeScreen: ❌ Logout failed:', error);
              Alert.alert(
                'Logout Error',
                '🔄 Logout mein problem! Try again kar bhai! Network check kar! 📶'
              );
            } finally {
              setIsLoading(false);
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  // Handle color recommendations with Gemini AI and fallback
  const handleColorRecommendations = async (result: FaceAnalysis) => {
console.log('🎨 Starting color recommendation process...');
console.log('🎨 Analysis ID:', result.data?.analysisId);

    setIsLoadingRecommendations(true);

    try {
      // Check if we have a valid analysis ID for Gemini AI recommendations
      if (result.data?.analysisId && result.data.analysisId !== 'undefined') {
console.log('🤖 Attempting to get Gemini AI recommendations for analysis:', result.data.analysisId);

        try {
          const recommendations = await apiService.getColorRecommendations(result.data.analysisId);
console.log('🤖 Gemini AI recommendations received:', recommendations);

          if (recommendations.success && recommendations.data?.outfits) {
console.log('✅ Using Gemini AI recommendations');
            setColorRecommendations(recommendations);
            setRecommendationSource('gemini');

            // Convert Gemini recommendations to color combinations format
            const combinations = recommendations.data.outfits.map((outfit, index) => ({
              id: `gemini-${result.data.analysisId}-${index}`,
              shirt: outfit.shirt?.hex || '#ffffff',
              pants: outfit.pants?.hex || '#1e40af',
              shoes: outfit.shoes?.hex || '#000000',
              description: outfit.outfitName || `AI Outfit ${index + 1}`,
              confidence: recommendations.data.confidence || 0.9,
              source: 'gemini' as const
            }));

            setLocalColorCombinations(combinations);
            setColorCombinations(combinations);
            setSelectedCombination(0);
            return; // Success with Gemini AI
          }
        } catch (geminiError: any) {
console.warn('🤖 Gemini AI recommendations failed:', geminiError.message);
          // Continue to fallback
        }
      } else {
console.log('⚠️ No valid analysis ID, skipping Gemini AI recommendations');
      }

      // Fallback to default color combinations
console.log('🎨 Using default color combinations');
      setRecommendationSource('default');
      setColorRecommendations(null); // Clear any previous AI recommendations

      // Generate default combinations based on detected colors
      const combinations = ColorService.parseApiRecommendations(result);
      const enhancedCombinations = combinations.map((combo, index) => ({
        ...combo,
        id: `default-${index}`,
        description: `${combo.description} (Default)`,
        source: 'default' as const
      }));

      setLocalColorCombinations(enhancedCombinations);
      setColorCombinations(enhancedCombinations);
      setSelectedCombination(0);

    } catch (error: any) {
console.error('❌ Color recommendation error:', error);

      // Emergency fallback - basic color combinations
console.log('🆘 Using emergency fallback colors');
      setRecommendationSource('default');
      setColorRecommendations(null);

      const emergencyColors = [
        {
          id: 'emergency-1',
          shirt: '#ffffff',
          pants: '#1e40af',
          shoes: '#000000',
          description: 'Classic White & Blue (Default)',
          confidence: 0.7,
          source: 'default' as const
        },
        {
          id: 'emergency-2',
          shirt: '#f8f9fa',
          pants: '#2c3e50',
          shoes: '#8b4513',
          description: 'Light Gray & Navy (Default)',
          confidence: 0.7,
          source: 'default' as const
        }
      ];

      setLocalColorCombinations(emergencyColors);
      setColorCombinations(emergencyColors);
      setSelectedCombination(0);

    } finally {
      setIsLoadingRecommendations(false);
    }
  };

  // Modal handlers
  const openOutfitModal = (outfit: any) => {
    setSelectedOutfit(outfit);
    setModalVisible(true);
  };

  const closeOutfitModal = () => {
    setModalVisible(false);
    setSelectedOutfit(null);
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Temporary debug panel */}
      {/* <AuthDebug /> */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity
            style={styles.userInfo}
            onPress={() => router.push('/settings')}
          >
            <View style={styles.userAvatar}>
              <Ionicons name="person-circle" size={40} color="#2563eb" />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>Hello, {user?.name}!</Text>
              <Text style={styles.userGender}>
                {user?.gender === 'male' ? '👨 Male' : '👩 Female'}
              </Text>
              {/*<Text style={styles.tapToEditText}>Tap to view profile</Text>*/}

            </View>
          </TouchableOpacity>
          <View style={styles.headerButtons}>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={async () => {
                try {
console.log('Manual refresh: Current user before refresh:', user);
                  await refreshProfile(true); // Force cache-busting refresh
console.log('Manual refresh: User after refresh:', user);
                  Alert.alert('Success', `Profile refreshed! Name: ${user?.name}, Gender: ${user?.gender}`);
                } catch (error: any) {
                  Alert.alert('Error', 'Failed to refresh profile: ' + error.message);
                }
              }}
            >
              <Ionicons name="refresh" size={20} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.refreshButton, { backgroundColor: '#10b981' }]}
              onPress={() => {
                try {
                  // Test notification system
                  Alert.alert('Test', '🎉 Ho gaya tera test! Notification system working! ✨');
                } catch (error) {
                  Alert.alert('Error', 'Notification test failed: ' + error);
                }
              }}
            >
              <Ionicons name="notifications" size={20} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Ionicons name="log-out" size={24} color="#64748b" />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Face Analysis</Text>
          <Text style={styles.headerSubtitle}>Upload your photo to get personalized color recommendations</Text>
        </View>
      </View>

      <View style={styles.uploadSection}>
        {selectedImage ? (
          <View style={styles.imageContainer}>
            <Image source={{ uri: selectedImage }} style={styles.uploadedImage} />
            <TouchableOpacity style={styles.removeButton} onPress={removeImage}>
              <Ionicons name="close-circle" size={30} color="#ff4757" />
            </TouchableOpacity>
            <View style={styles.imageActions}>
              <TouchableOpacity style={styles.changeButton} onPress={showImageOptions}>
                <Ionicons name="camera" size={20} color="#fff" />
                <Text style={styles.changeButtonText}>Change Photo</Text>
              </TouchableOpacity>

              {!analysisResult && (
                <TouchableOpacity
                  style={[styles.analyzeButton, isAnalyzing && styles.disabledButton]}
                  onPress={analyzeFace}
                  disabled={isAnalyzing}
                >
                  <View style={styles.analyzeButtonContent}>
                    {isAnalyzing ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <Ionicons name="analytics" size={20} color="#fff" />
                    )}
                    <Text style={styles.analyzeButtonText}>
                      {isAnalyzing ? 'Analyzing...' : 'Analyze Face'}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
            </View>
          </View>
        ) : (
          <Animated.View style={[styles.uploadArea, { transform: [{ scale: scaleAnim }] }]}>
            <TouchableOpacity
              style={styles.uploadButton}
              onPress={showImageOptions}
              disabled={isLoading}
            >
              <View style={styles.uploadButtonContent}>
                <Ionicons
                  name="cloud-upload-outline"
                  size={50}
                  color="#2563eb"
                  style={styles.uploadIcon}
                />
                <Text style={styles.uploadText}>
                  {isLoading ? 'Loading...' : 'Upload Photo'}
                </Text>
                <Text style={styles.uploadSubtext}>
                  Tap to select from gallery or take a photo
                </Text>
              </View>
            </TouchableOpacity>
          </Animated.View>
        )}
      </View>

      {analysisResult && analysisResult.data?.faceDetected && (
        <View style={styles.analysisSection}>
          <Text style={styles.analysisSectionTitle}>Analysis Results</Text>

          {/* Analysis Summary */}
          <View style={styles.analysisSummaryCard}>
            <View style={styles.analysisSummaryHeader}>
              <Ionicons name="checkmark-circle" size={20} color="#27ae60" />
              <Text style={styles.analysisSummaryTitle}>Analysis Complete</Text>
            </View>
            <Text style={styles.analysisSummaryText}>
              Processing Time: {analysisResult.data?.processingTime || 0}ms |
              Confidence: {Math.round((analysisResult.data?.confidence || 0) * 100)}%
            </Text>
          </View>

          {/* Face Colors Section - Only show if colors data exists */}
          {analysisResult.data?.colors && (
            <View style={styles.analysisCard}>
              <View style={styles.analysisHeader}>
                <Ionicons name="color-palette" size={24} color="#667eea" />
                <Text style={styles.analysisTitle}>Detected Colors</Text>
              </View>
              <View style={styles.colorsGrid}>
                {analysisResult.data.colors.skinTone && (
                  <View style={styles.colorItem}>
                    <View style={[styles.colorCircle, { backgroundColor: analysisResult.data.colors.skinTone?.hex || '#fdbcb4' }]} />
                    <Text style={styles.colorName}>Skin Tone</Text>
                    <Text style={styles.colorValue}>{analysisResult.data.colors.skinTone?.primary || 'Medium'}</Text>
                    <Text style={styles.colorHex}>{analysisResult.data.colors.skinTone?.hex || '#fdbcb4'}</Text>
                  </View>
                )}
                {analysisResult.data.colors.hairColor && (
                  <View style={styles.colorItem}>
                    <View style={[styles.colorCircle, { backgroundColor: analysisResult.data.colors.hairColor?.hex || '#4a2c17' }]} />
                    <Text style={styles.colorName}>Hair Color</Text>
                    <Text style={styles.colorValue}>{analysisResult.data.colors.hairColor?.primary || 'Brown'}</Text>
                    <Text style={styles.colorHex}>{analysisResult.data.colors.hairColor?.hex || '#4a2c17'}</Text>
                  </View>
                )}
                {analysisResult.data.colors.eyeColor && (
                  <View style={styles.colorItem}>
                    <View style={[styles.colorCircle, { backgroundColor: analysisResult.data.colors.eyeColor?.hex || '#8b4513' }]} />
                    <Text style={styles.colorName}>Eye Color</Text>
                    <Text style={styles.colorValue}>{analysisResult.data.colors.eyeColor?.primary || 'Brown'}</Text>
                    <Text style={styles.colorHex}>{analysisResult.data.colors.eyeColor?.hex || '#8b4513'}</Text>
                  </View>
                )}
                {analysisResult.data.colors.lipColor && (
                  <View style={styles.colorItem}>
                    <View style={[styles.colorCircle, { backgroundColor: analysisResult.data.colors.lipColor?.hex || '#d2691e' }]} />
                    <Text style={styles.colorName}>Lip Color</Text>
                    <Text style={styles.colorValue}>{analysisResult.data.colors.lipColor?.primary || 'Natural'}</Text>
                    <Text style={styles.colorHex}>{analysisResult.data.colors.lipColor?.hex || '#d2691e'}</Text>
                  </View>
                )}
              </View>
            </View>
          )}

          {/* Face Features Section - Only show if features data exists */}
          {(analysisResult.data.features || analysisResult.data.facialFeatures) && (
            <View style={styles.analysisCard}>
              <View style={styles.analysisHeader}>
                <Ionicons name="scan" size={24} color="#667eea" />
                <Text style={styles.analysisTitle}>Facial Features</Text>
              </View>
              <View style={styles.featuresGrid}>
                <View style={styles.featureItem}>
                  <Text style={styles.featureLabel}>Face Shape</Text>
                  <Text style={styles.featureValue}>
                    {analysisResult.data.features?.faceShape ||
                     analysisResult.data.facialFeatures?.faceShape ||
                     'Not detected'}
                  </Text>
                </View>
                <View style={styles.featureItem}>
                  <Text style={styles.featureLabel}>Eye Shape</Text>
                  <Text style={styles.featureValue}>
                    {analysisResult.data.features?.eyeShape ||
                     analysisResult.data.facialFeatures?.eyeShape ||
                     'Not detected'}
                  </Text>
                </View>
                <View style={styles.featureItem}>
                  <Text style={styles.featureLabel}>Nose Shape</Text>
                  <Text style={styles.featureValue}>
                    {analysisResult.data.features?.noseShape ||
                     analysisResult.data.facialFeatures?.noseShape ||
                     'Not detected'}
                  </Text>
                </View>
                <View style={styles.featureItem}>
                  <Text style={styles.featureLabel}>Lip Shape</Text>
                  <Text style={styles.featureValue}>
                    {analysisResult.data.features?.lipShape ||
                     analysisResult.data.facialFeatures?.lipShape ||
                     'Not detected'}
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Face Dimensions Section - Only show if dimensions data exists */}
          {(analysisResult.data.dimensions || analysisResult.data.faceDimensions) && (
            <View style={styles.analysisCard}>
              <View style={styles.analysisHeader}>
                <Ionicons name="resize" size={24} color="#667eea" />
                <Text style={styles.analysisTitle}>Face Dimensions</Text>
              </View>
              <View style={styles.dimensionsGrid}>
                <View style={styles.dimensionItem}>
                  <Text style={styles.dimensionLabel}>Face Length</Text>
                  <Text style={styles.dimensionValue}>
                    {analysisResult.data.dimensions?.faceLength ||
                     analysisResult.data.faceDimensions?.faceLength ||
                     analysisResult.data.dimensions?.faceHeight ||
                     analysisResult.data.faceDimensions?.faceHeight || 0}px
                  </Text>
                </View>
                <View style={styles.dimensionItem}>
                  <Text style={styles.dimensionLabel}>Face Width</Text>
                  <Text style={styles.dimensionValue}>
                    {analysisResult.data.dimensions?.faceWidth ||
                     analysisResult.data.faceDimensions?.faceWidth || 0}px
                  </Text>
                </View>
                <View style={styles.dimensionItem}>
                  <Text style={styles.dimensionLabel}>Length/Width Ratio</Text>
                  <Text style={styles.dimensionValue}>
                    {(analysisResult.data.dimensions?.lengthToWidthRatio ||
                      analysisResult.data.faceDimensions?.lengthToWidthRatio || 0).toFixed(2)}
                  </Text>
                </View>
                <View style={styles.dimensionItem}>
                  <Text style={styles.dimensionLabel}>Confidence</Text>
                  <Text style={styles.dimensionValue}>
                    {Math.round((analysisResult.data.confidence || 0) * 100)}%
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Color Recommendations Section */}
          {isLoadingRecommendations && (
            <View style={styles.loadingCard}>
              <ActivityIndicator size="large" color="#667eea" />
              <Text style={styles.loadingText}>Getting personalized color recommendations...</Text>
            </View>
          )}

          {colorRecommendations && colorRecommendations.success && (
            <View style={styles.recommendationsCard}>
              <View style={styles.recommendationsHeader}>
                <Ionicons name="sparkles" size={24} color="#e91e63" />
                <View style={styles.recommendationTitleContainer}>
                  <Text style={styles.recommendationsTitle}>🤖 Gemini AI Recommendations</Text>
                  <View style={styles.aiSourceBadge}>
                    <Text style={styles.aiSourceText}>Powered by Google Gemini</Text>
                  </View>
                </View>
              </View>

              {/* AI Advice */}
              <View style={styles.adviceCard}>
                <Text style={styles.adviceText}>{colorRecommendations.data.advice}</Text>
                <Text style={styles.seasonalType}>Seasonal Type: {colorRecommendations.data.colorPalette.seasonalType}</Text>
              </View>

              {/* Outfit Recommendations */}
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.combinationsScroll}>
                {colorRecommendations.data.outfits.map((outfit, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.outfitCard,
                      selectedCombination === index && styles.selectedCombination
                    ]}
                    onPress={() => {
                      setSelectedCombination(index);
                      openOutfitModal(outfit);
                    }}
                  >
                    <Text style={styles.outfitName}>{outfit.outfitName}</Text>

                    {/* Color Preview */}
                    <View style={styles.colorPreview}>
                      <View style={[styles.colorBox, { backgroundColor: outfit.shirt?.hex || '#ffffff' }]} />
                      <View style={[styles.colorBox, { backgroundColor: outfit.pants?.hex || '#1e40af' }]} />
                      <View style={[styles.colorBox, { backgroundColor: outfit.shoes?.hex || '#000000' }]} />
                    </View>

                    {/* Color Details */}
                    <View style={styles.colorDetails}>
                      <View style={styles.colorDetailItem}>
                        <Text style={styles.colorDetailLabel}>👔 Shirt</Text>
                        <Text style={styles.colorDetailName}>{outfit.shirt?.color || 'White'}</Text>
                        <Text style={styles.colorDetailHex}>{outfit.shirt?.hex || '#ffffff'}</Text>
                      </View>
                      <View style={styles.colorDetailItem}>
                        <Text style={styles.colorDetailLabel}>👖 Pants</Text>
                        <Text style={styles.colorDetailName}>{outfit.pants?.color || 'Blue'}</Text>
                        <Text style={styles.colorDetailHex}>{outfit.pants?.hex || '#1e40af'}</Text>
                      </View>
                      <View style={styles.colorDetailItem}>
                        <Text style={styles.colorDetailLabel}>👞 Shoes</Text>
                        <Text style={styles.colorDetailName}>{outfit.shoes?.color || 'Black'}</Text>
                        <Text style={styles.colorDetailHex}>{outfit.shoes?.hex || '#000000'}</Text>
                      </View>
                    </View>

                    <Text style={styles.outfitReason} numberOfLines={3}>
                      {outfit.overallReason}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}

          {/* Fallback for default color combinations */}
          {!colorRecommendations && colorCombinations.length > 0 && (
            <View style={styles.recommendationsCard}>
              <View style={styles.recommendationsHeader}>
                <Ionicons name="color-palette" size={24} color="#667eea" />
                <View style={styles.recommendationTitleContainer}>
                  <Text style={styles.recommendationsTitle}>
                    {recommendationSource === 'default' ? '🎨 Default Color Recommendations' : 'Color Recommendations'}
                  </Text>
                  {recommendationSource === 'default' && (
                    <View style={styles.defaultSourceBadge}>
                      <Text style={styles.defaultSourceText}>Based on Color Analysis</Text>
                    </View>
                  )}
                </View>
              </View>

              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.combinationsScroll}>
                {colorCombinations.map((combination, index) => (
                  <TouchableOpacity
                    key={combination.id}
                    style={[
                      styles.combinationCard,
                      selectedCombination === index && styles.selectedCombination
                    ]}
                    onPress={() => setSelectedCombination(index)}
                  >
                    <Text style={styles.combinationNumber}>#{index + 1}</Text>
                    <View style={styles.colorPreview}>
                      <View style={[styles.colorBox, { backgroundColor: combination.shirt }]} />
                      <View style={[styles.colorBox, { backgroundColor: combination.pants }]} />
                      <View style={[styles.colorBox, { backgroundColor: combination.shoes }]} />
                    </View>
                    <Text style={styles.combinationDescription} numberOfLines={2}>
                      {combination.description}
                    </Text>
                    <Text style={styles.confidenceText}>
                      {Math.round((combination.confidence || 0) * 100)}% match
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}

          {/* View on 3D Model Button */}
          {(colorRecommendations || colorCombinations.length > 0) && (
            <View style={styles.viewModelSection}>
              <TouchableOpacity
                style={styles.viewModelButton}
                onPress={() => {
                  // console.log('Navigation: User gender is:', user?.gender); // REMOVED: Sensitive gender info
                  if (user?.gender === 'male') {
                    // console.log('Navigation: Redirecting to male model (explore)'); // REMOVED: Sensitive navigation info
                    router.push('/explore');
                  } else if (user?.gender === 'female') {
                    // console.log('Navigation: Redirecting to female model'); // REMOVED: Sensitive navigation info
                    router.push('/female');
                  } else {
console.warn('Navigation: Unknown gender, defaulting to male model');
                    router.push('/explore');
                  }
                }}
              >
                <View style={styles.viewModelButtonContent}>
                  <Ionicons name="shirt" size={20} color="#fff" />
                  <Text style={styles.viewModelButtonText}>
                    View on {user?.gender === 'male' ? 'Male' : user?.gender === 'female' ? 'Female' : 'Default'} Model
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}

      <View style={styles.infoSection}>
        <View style={styles.infoCard}>
          <Ionicons name="shield-checkmark" size={24} color="#2ed573" />
          <Text style={styles.infoTitle}>Secure Upload</Text>
          <Text style={styles.infoText}>Your photos are processed securely and privately</Text>
        </View>

        <View style={styles.infoCard}>
          <Ionicons name="resize" size={24} color="#3742fa" />
          <Text style={styles.infoTitle}>Auto Resize</Text>
          <Text style={styles.infoText}>Images are automatically optimized for best quality</Text>
        </View>

        <View style={styles.infoCard}>
          <Ionicons name="flash" size={24} color="#ffa502" />
          <Text style={styles.infoTitle}>Fast Processing</Text>
          <Text style={styles.infoText}>Quick upload and processing for instant results</Text>
        </View>
      </View>

      {hasPermission === false && (
        <View style={styles.permissionCard}>
          <Ionicons name="warning" size={24} color="#ff6b6b" />
          <Text style={styles.permissionTitle}>Gallery Access Limited</Text>
          <Text style={styles.permissionText}>
            Grant gallery permission for easier photo selection
          </Text>
          <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Color Recommendation Detail Modal */}
      <ColorRecommendationModal
        visible={modalVisible}
        onClose={closeOutfitModal}
        outfit={selectedOutfit}
        seasonalType={colorRecommendations?.data?.colorPalette?.seasonalType}
        advice={colorRecommendations?.data?.advice}
      />
    </ScrollView>
  );
}

const { width: screenWidth } = Dimensions.get('window');
const isTablet = screenWidth > 768;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  contentContainer: {
    flexGrow: 1,
    maxWidth: isTablet ? 800 : '100%',
    alignSelf: 'center',
    width: '100%',
    paddingBottom: 100,
  },
  header: {
    backgroundColor: '#f8fafc',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 32,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 24,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDetails: {
    marginLeft: 12,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  userGender: {
    fontSize: 14,
    color: '#64748b',
  },
  tapToEditText: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 2,
    fontStyle: 'italic',
  },
  debugGender: {
    fontSize: 10,
    color: '#64748b',
    marginTop: 2,
    fontFamily: 'monospace',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f1f5f9',
  },
  logoutButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f1f5f9',
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  uploadSection: {
    padding: 24,
    alignItems: 'center',
  },
  uploadArea: {
    width: width - 48,
    height: 250,
  },
  uploadButton: {
    flex: 1,
    borderRadius: 16,
    backgroundColor: '#f8fafc',
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderStyle: 'dashed',
  },
  uploadButtonContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  uploadIcon: {
    marginBottom: 16,
  },
  uploadText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  uploadSubtext: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
  },
  imageContainer: {
    position: 'relative',
    alignItems: 'center',
  },
  uploadedImage: {
    width: width - 40,
    height: 250,
    borderRadius: 20,
    resizeMode: 'cover',
  },
  removeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#fff',
    borderRadius: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  imageActions: {
    marginTop: 15,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
  },
  changeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#64748b',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
  },
  changeButtonText: {
    color: '#fff',
    fontWeight: '600',
    marginLeft: 8,
    fontSize: 16,
  },
  analyzeButton: {
    backgroundColor: '#dc2626',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  analyzeButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  analyzeButtonText: {
    color: '#fff',
    fontWeight: '600',
    marginLeft: 8,
    fontSize: 16,
  },
  disabledButton: {
    opacity: 0.7,
  },
  analysisSection: {
    padding: 20,
    paddingTop: 0,
  },
  analysisSectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 20,
    textAlign: 'center',
  },
  analysisCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  analysisTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginLeft: 10,
  },
  analysisDetails: {
    gap: 8,
  },
  analysisText: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
  },
  recommendationsCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  recommendationsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  recommendationsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginLeft: 10,
  },
  combinationsScroll: {
    marginBottom: 20,
  },
  combinationCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginRight: 15,
    width: 150,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCombination: {
    borderColor: '#667eea',
    backgroundColor: '#f0f2ff',
  },
  combinationNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#667eea',
    marginBottom: 10,
    textAlign: 'center',
  },
  colorPreview: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    gap: 5,
  },
  colorBox: {
    width: 30,
    height: 30,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  combinationDescription: {
    fontSize: 12,
    color: '#6c757d',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 16,
  },
  confidenceText: {
    fontSize: 11,
    color: '#28a745',
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
  },
  colorLabels: {
    gap: 2,
  },
  colorLabel: {
    fontSize: 10,
    color: '#6c757d',
    textAlign: 'center',
  },
  viewModelButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  viewModelButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewModelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoSection: {
    padding: 24,
    paddingTop: 16,
  },
  infoCard: {
    backgroundColor: '#ffffff',
    padding: 24,
    borderRadius: 16,
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginTop: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 22,
  },
  permissionCard: {
    backgroundColor: '#fef3c7',
    margin: 24,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fbbf24',
  },
  permissionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#92400e',
    marginTop: 12,
    marginBottom: 8,
  },
  permissionText: {
    fontSize: 14,
    color: '#92400e',
    textAlign: 'center',
    marginBottom: 15,
    lineHeight: 20,
  },
  permissionButton: {
    backgroundColor: '#f59e0b',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
  },
  permissionButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  // New styles for enhanced analysis display
  viewModelSection: {
    padding: 24,
    alignItems: 'center',
  },
  analysisSummaryCard: {
    backgroundColor: '#f0fdf4',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  analysisSummaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  analysisSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#16a34a',
    marginLeft: 8,
  },
  analysisSummaryText: {
    fontSize: 14,
    color: '#1e293b',
    textAlign: 'center',
    lineHeight: 20,
  },
  testButton: {
    backgroundColor: '#2563eb',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    marginTop: 12,
    alignSelf: 'center',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  colorsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
  },
  colorItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  colorCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: '#e2e8f0',
  },
  colorName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  colorValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#667eea',
    marginBottom: 2,
  },
  colorHex: {
    fontSize: 11,
    color: '#666',
    fontFamily: 'monospace',
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  featureItem: {
    width: '48%',
    marginBottom: 15,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
  },
  featureLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  featureValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textTransform: 'capitalize',
  },
  dimensionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  dimensionItem: {
    width: '48%',
    marginBottom: 15,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
  },
  dimensionLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  dimensionValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingCard: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#667eea',
    textAlign: 'center',
  },
  adviceCard: {
    backgroundColor: '#f0f4ff',
    margin: 10,
    padding: 15,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#667eea',
  },
  adviceText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  seasonalType: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#667eea',
  },
  outfitCard: {
    width: 280,
    backgroundColor: '#fff',
    marginHorizontal: 10,
    padding: 15,
    borderRadius: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  outfitName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  colorDetails: {
    marginTop: 10,
    marginBottom: 10,
  },
  colorDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 4,
  },
  colorDetailLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
    flex: 1,
  },
  colorDetailName: {
    fontSize: 12,
    color: '#333',
    flex: 2,
    textAlign: 'center',
  },
  colorDetailHex: {
    fontSize: 10,
    color: '#666',
    fontFamily: 'monospace',
    flex: 1,
    textAlign: 'right',
  },
  outfitReason: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    textAlign: 'center',
    marginTop: 8,
  },
  recommendationTitleContainer: {
    flex: 1,
  },
  aiSourceBadge: {
    backgroundColor: '#e91e63',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginTop: 4,
    alignSelf: 'flex-start',
  },
  aiSourceText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  defaultSourceBadge: {
    backgroundColor: '#667eea',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginTop: 4,
    alignSelf: 'flex-start',
  },
  defaultSourceText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

// Main component that handles authentication check
export default function HomeScreen() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { setUserColors } = useColor();

// console.log('HomeScreen render - isAuthenticated:', isAuthenticated, 'isLoading:', isLoading, 'user:', user?.name, 'userGender:', user?.gender); // REMOVED: Sensitive user data

  // Show loading while checking auth
  if (isLoading) {
console.log('HomeScreen: Showing loading screen');
    return <AuthWrapper />;
  }

  // If not authenticated, show auth screens
  if (!isAuthenticated) {
console.log('HomeScreen: User not authenticated, showing auth wrapper');
    return <AuthWrapper />;
  }

  // If authenticated, show the main home screen
console.log('HomeScreen: User authenticated, showing main home screen');
  return <AuthenticatedHomeScreen />;
}
