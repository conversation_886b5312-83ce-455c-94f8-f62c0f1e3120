import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';

export const AuthDebug: React.FC = () => {
  const { user, isLoading, isAuthenticated, refreshProfile } = useAuth();
  const [tokenInfo, setTokenInfo] = useState<string>('');
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    loadDebugInfo();
  }, [user, isAuthenticated]);

  const loadDebugInfo = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      setTokenInfo(token ? `Token exists (***HIDDEN***)` : 'No token'); // REMOVED: Sensitive token data
      
      setDebugInfo({
        isAuthenticated,
        isLoading,
        hasUser: !!user,
        userName: user?.name ? '***HIDDEN***' : undefined, // REMOVED: Sensitive user name
        userGender: user?.gender,
        userId: user?.id ? '***HIDDEN***' : undefined, // REMOVED: Sensitive user ID
        timestamp: new Date().toLocaleTimeString()
      });
    } catch (error) {
console.error('Debug info load error:', error);
    }
  };

  const testApiCall = async () => {
    try {
      const profile = await apiService.getProfile();
      Alert.alert('API Test Success', `Profile loaded: ${profile.name} (${profile.gender})`);
    } catch (error: any) {
      Alert.alert('API Test Failed', error.message);
    }
  };

  const clearAuth = async () => {
    try {
      await AsyncStorage.removeItem('authToken');
      Alert.alert('Success', 'Token cleared. Please restart the app.');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔍 Auth Debug Panel</Text>
      
      <View style={styles.infoSection}>
        <Text style={styles.label}>Authentication State:</Text>
        <Text style={styles.value}>
          {isLoading ? '⏳ Loading...' : isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
        </Text>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.label}>User Info:</Text>
        <Text style={styles.value}>
          {user ? `👤 ***HIDDEN*** (${user.gender})` : '👤 No user data'}
        </Text>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.label}>Token Status:</Text>
        <Text style={styles.value}>{tokenInfo}</Text>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.label}>Verification Status:</Text>
        <Text style={styles.value}>
          ✅ No pending verification
        </Text>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.label}>Debug Info:</Text>
        <Text style={styles.debugText}>
          {JSON.stringify(debugInfo, null, 2)}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={loadDebugInfo}>
          <Text style={styles.buttonText}>🔄 Refresh</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={refreshProfile}>
          <Text style={styles.buttonText}>👤 Refresh Profile</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testApiCall}>
          <Text style={styles.buttonText}>🧪 Test API</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.dangerButton]} onPress={clearAuth}>
          <Text style={styles.buttonText}>🗑️ Clear Token</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007bff',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007bff',
    textAlign: 'center',
    marginBottom: 15,
  },
  infoSection: {
    marginBottom: 10,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 2,
  },
  value: {
    fontSize: 14,
    color: '#212529',
    backgroundColor: '#e9ecef',
    padding: 8,
    borderRadius: 5,
  },
  debugText: {
    fontSize: 12,
    color: '#6c757d',
    backgroundColor: '#e9ecef',
    padding: 8,
    borderRadius: 5,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 15,
  },
  button: {
    backgroundColor: '#007bff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 5,
    minWidth: 80,
  },
  dangerButton: {
    backgroundColor: '#dc3545',
  },
  buttonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
