import { useCallback, useEffect, useState } from 'react';
import { MostRecentRecommendation, userRecommendationService } from '../services/userRecommendationService';

export interface UseUserRecommendationsReturn {
  // State
  isLoading: boolean;
  hasRecommendations: boolean;
  mostRecentRecommendation: MostRecentRecommendation | null;
  recommendedColors: string[] | null;
  error: string | null;

  // Actions
  refreshRecommendations: () => Promise<void>;
  clearCache: () => void;
  forceRefreshOnMount: () => Promise<void>;
  triggerNavigationRefresh: () => void;
}

export const useUserRecommendations = (autoFetch = true, refreshOnNavigation = false): UseUserRecommendationsReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasRecommendations, setHasRecommendations] = useState(false);
  const [mostRecentRecommendation, setMostRecentRecommendation] = useState<MostRecentRecommendation | null>(null);
  const [recommendedColors, setRecommendedColors] = useState<string[] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [refreshCounter, setRefreshCounter] = useState(0);

  /**
   * Fetch user recommendations (always fresh from database)
   */
  const fetchRecommendations = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

console.log('🎨 useUserRecommendations: Fetching fresh recommendations from database...');

      // Get most recent recommendation (always fresh)
      const recentRecommendation = await userRecommendationService.getMostRecentRecommendation();
      setMostRecentRecommendation(recentRecommendation);
      setHasRecommendations(recentRecommendation.hasRecommendations);

      // Get recommended colors
      const colors = await userRecommendationService.getRecommendedColorCombinations();
      setRecommendedColors(colors);

      // console.log('🎨 useUserRecommendations: Fresh fetch completed', {
      //   hasRecommendations: recentRecommendation.hasRecommendations,
      //   colorsCount: colors?.length || 0
      // }); // REMOVED: Debug logging

      // Show notification if user has no recommendations
      if (!recentRecommendation.hasRecommendations) {
        // console.log('🎨 useUserRecommendations: User has no recommendations - showing helpful message'); // REMOVED: Debug logging
        try {
          const { notificationService } = require('../services/notificationService');
          notificationService.noColorRecommendations();
        } catch (notifError) {
          // console.log('🎨 useUserRecommendations: Could not show no recommendations notification:', notifError); // REMOVED: Debug logging
        }
      }

    } catch (err) {
console.error('🎨 useUserRecommendations: Error fetching fresh recommendations:', err);

      // Check if this is a "no recommendations" case (404 error)
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch recommendations';
      if (errorMessage.includes('404') || errorMessage.includes('not found')) {
        // console.log('🎨 useUserRecommendations: No recommendations found - user needs to do face analysis'); // REMOVED: Debug logging
        // Show user-friendly message for no recommendations
        try {
          const { notificationService } = require('../services/notificationService');
          notificationService.noColorRecommendations();
        } catch (notifError) {
          // console.log('🎨 useUserRecommendations: Could not show notification:', notifError); // REMOVED: Debug logging
        }
      } else {
        // Show general error for other issues
        try {
          const { notificationService } = require('../services/notificationService');
          notificationService.error('🎨 Color recommendations load nahi ho saka! Try again kar! 🔄', 'Check your internet connection');
        } catch (notifError) {
          // console.log('🎨 useUserRecommendations: Could not show error notification:', notifError); // REMOVED: Debug logging
        }
      }

      setError(errorMessage);
      setHasRecommendations(false);
      setMostRecentRecommendation(null);
      setRecommendedColors(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh recommendations (always fresh since no caching)
   */
  const refreshRecommendations = useCallback(async () => {
console.log('🎨 useUserRecommendations: Refreshing recommendations (always fresh)...');
    await fetchRecommendations();
  }, [fetchRecommendations]);

  /**
   * Clear cache
   */
  const clearCache = useCallback(() => {
console.log('🎨 useUserRecommendations: Clearing cache...');
    userRecommendationService.clearCache();
    setMostRecentRecommendation(null);
    setRecommendedColors(null);
    setHasRecommendations(false);
    setError(null);
  }, []);

  /**
   * Force refresh on mount (same as regular fetch since no caching)
   */
  const forceRefreshOnMount = useCallback(async () => {
console.log('🎨 useUserRecommendations: Force refresh on mount (always fresh since no caching)');
    await fetchRecommendations();
  }, [fetchRecommendations]);

  /**
   * Trigger refresh on navigation - increments counter to force useEffect re-run
   */
  const triggerNavigationRefresh = useCallback(() => {
console.log('🎨 useUserRecommendations: Triggering navigation refresh...');
    setRefreshCounter(prev => prev + 1);
  }, []);

  /**
   * Auto-fetch on mount if enabled (always fresh data)
   * Also triggers on navigation refresh counter change
   */
  useEffect(() => {
    if (autoFetch) {
console.log('🎨 useUserRecommendations: Auto-fetch enabled - fetching fresh data (counter:', refreshCounter, ')');
      fetchRecommendations();
    }
  }, [autoFetch, fetchRecommendations, refreshCounter]);

  return {
    // State
    isLoading,
    hasRecommendations,
    mostRecentRecommendation,
    recommendedColors,
    error,

    // Actions
    refreshRecommendations,
    clearCache,
    forceRefreshOnMount,
    triggerNavigationRefresh
  };
};

export default useUserRecommendations;
