import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserRecommendation {
  _id: string;
  createdAt: string;
  colorPalette: {
    bestColors: string[];
    avoidColors: string[];
    generalAdvice: string;
  };
  analysisId?: string;
  userId?: string;
}

// New interface for the latest recommendation response
export interface LatestRecommendationResponse {
  success: boolean;
  data: {
    _id: string;
    userId: string;
    faceAnalysisId: {
      colors: {
        hairColor: { rgb: { r: number; g: number; b: number }; primary: string; hex: string; confidence: number };
        skinTone: { rgb: { r: number; g: number; b: number }; primary: string; hex: string; confidence: number };
        eyeColor: { rgb: { r: number; g: number; b: number }; primary: string; hex: string; confidence: number };
        lipColor: { rgb: { r: number; g: number; b: number }; primary: string; hex: string; confidence: number };
      };
      facialFeatures: {
        faceShape: string;
        eyeShape: string;
        eyeDistance: string;
        eyebrowShape: string;
        noseShape: string;
        lipShape: string;
      };
      _id: string;
      createdAt: string;
    };
    aiService: string;
    aiModel: string;
    recommendations: Array<{
      shirt: { color: string; hex: string; reason: string };
      pants: { color: string; hex: string; reason: string };
      shoes: { color: string; hex: string; reason: string };
      outfitName: string;
      overallReason: string;
    }>;
    colorPalette: {
      bestColors: string[];
      avoidColors: string[];
      seasonalType: string;
    };
    generalAdvice: string;
    favoriteOutfits: any[];
    processingTime: number;
    confidence: number;
    isActive: boolean;
    version: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
  };
}

export interface RecommendationHistoryResponse {
  success: boolean;
  data: {
    recommendations: UserRecommendation[];
  };
}

export interface MostRecentRecommendation {
  hasRecommendations: boolean;
  recommendation: UserRecommendation | null;
  colorPalette: {
    bestColors: string[];
    avoidColors: string[];
    generalAdvice: string;
  } | null;
}

class UserRecommendationService {
  private baseUrl = 'https://faceapp-ttwh.onrender.com/api';
  // Removed all caching - every call fetches fresh data from database

  /**
   * Fetch the latest recommendation directly from the new endpoint
   */
  async fetchLatestRecommendation(): Promise<LatestRecommendationResponse | null> {
    try {
console.log('🎨 UserRecommendation: Fetching latest recommendation...');

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
console.log('🎨 UserRecommendation: No token found, user not authenticated');
        return null;
      }

      const response = await fetch(`${this.baseUrl}/face/recommendations/latest`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
console.log('🎨 UserRecommendation: No recommendations found for user (404) - user needs to do face analysis first');
          return null;
        } else {
console.error('🎨 UserRecommendation: Latest API request failed:', response.status);
          throw new Error(`API request failed with status ${response.status}`);
        }
      }

      const data: LatestRecommendationResponse = await response.json();
console.log('🎨 UserRecommendation: Fetched latest recommendation:', data.data?._id || 'none');

      return data;
    } catch (error) {
console.error('🎨 UserRecommendation: Error fetching latest recommendation:', error);
      return null;
    }
  }

  /**
   * Fetch user's recommendation history from API (legacy method)
   */
  async fetchRecommendationHistory(): Promise<RecommendationHistoryResponse | null> {
    try {
console.log('🎨 UserRecommendation: Fetching recommendation history...');
      
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
console.log('🎨 UserRecommendation: No token found, user not authenticated');
        return null;
      }

      const response = await fetch(`${this.baseUrl}/face/recommendations/history`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
console.error('🎨 UserRecommendation: API request failed:', response.status);
        return null;
      }

      const data: RecommendationHistoryResponse = await response.json();
console.log('🎨 UserRecommendation: Fetched recommendations:', data.data?.recommendations?.length || 0);
      
      return data;
    } catch (error) {
console.error('🎨 UserRecommendation: Error fetching recommendations:', error);
      return null;
    }
  }

  /**
   * Get the most recent recommendation using the new latest endpoint
   * NO CACHING - Always fetches fresh data from database
   */
  async getMostRecentRecommendation(): Promise<MostRecentRecommendation> {
    try {
console.log('🎨 UserRecommendation: Fetching fresh latest recommendation from database (no cache)...');

      const latestResponse = await this.fetchLatestRecommendation();

      if (!latestResponse || !latestResponse.success || !latestResponse.data) {
console.log('🎨 UserRecommendation: No latest recommendation found in database');
        return {
          hasRecommendations: false,
          recommendation: null,
          colorPalette: null
        };
      }

      const latestData = latestResponse.data;

      // Convert the new format to the expected UserRecommendation format
      const convertedRecommendation: UserRecommendation = {
        _id: latestData._id,
        createdAt: latestData.createdAt,
        colorPalette: {
          bestColors: latestData.colorPalette.bestColors,
          avoidColors: latestData.colorPalette.avoidColors,
          generalAdvice: latestData.generalAdvice
        },
        analysisId: latestData.faceAnalysisId._id,
        userId: latestData.userId
      };

console.log('🎨 UserRecommendation: Fresh latest recommendation from database:', {
        id: convertedRecommendation._id,
        createdAt: convertedRecommendation.createdAt,
        bestColorsCount: convertedRecommendation.colorPalette?.bestColors?.length || 0,
        avoidColorsCount: convertedRecommendation.colorPalette?.avoidColors?.length || 0
      });

      return {
        hasRecommendations: true,
        recommendation: convertedRecommendation,
        colorPalette: convertedRecommendation.colorPalette
      };
    } catch (error) {
console.error('🎨 UserRecommendation: Error getting fresh latest recommendation:', error);
      return {
        hasRecommendations: false,
        recommendation: null,
        colorPalette: null
      };
    }
  }

  /**
   * Get the raw latest recommendation data for model pages
   * Returns the full recommendation structure with outfit details
   */
  async getLatestRecommendationData(): Promise<LatestRecommendationResponse['data'] | null> {
    try {
console.log('🎨 UserRecommendation: Getting raw latest recommendation data...');

      const latestResponse = await this.fetchLatestRecommendation();

      if (!latestResponse || !latestResponse.success || !latestResponse.data) {
console.log('🎨 UserRecommendation: No latest recommendation data found');
        return null;
      }

console.log('🎨 UserRecommendation: Raw latest recommendation data retrieved:', {
        id: latestResponse.data._id,
        outfitCount: latestResponse.data.recommendations?.length || 0,
        bestColorsCount: latestResponse.data.colorPalette?.bestColors?.length || 0
      });

      return latestResponse.data;
    } catch (error) {
console.error('🎨 UserRecommendation: Error getting raw latest recommendation data:', error);
      return null;
    }
  }

  /**
   * Get color combinations from the most recent recommendation
   * Returns null if no recommendations or if bestColors is empty
   */
  async getRecommendedColorCombinations(): Promise<string[] | null> {
    try {
      const recentRecommendation = await this.getMostRecentRecommendation();
      
      if (!recentRecommendation.hasRecommendations || !recentRecommendation.colorPalette) {
console.log('🎨 UserRecommendation: No color recommendations available');
        return null;
      }

      const bestColors = recentRecommendation.colorPalette.bestColors;
      if (!bestColors || bestColors.length === 0) {
console.log('🎨 UserRecommendation: No best colors in recommendation');
        return null;
      }

console.log('🎨 UserRecommendation: Recommended colors:', bestColors);
      return bestColors;
    } catch (error) {
console.error('🎨 UserRecommendation: Error getting recommended colors:', error);
      return null;
    }
  }

  /**
   * Clear cache method (no-op since caching is disabled)
   */
  clearCache(): void {
console.log('🎨 UserRecommendation: Cache clearing disabled - always fetching fresh data');
    // No-op since we removed all caching
  }

  /**
   * Check if user has any recommendations
   */
  async hasRecommendations(): Promise<boolean> {
    const recentRecommendation = await this.getMostRecentRecommendation();
    return recentRecommendation.hasRecommendations;
  }
}

// Export singleton instance
export const userRecommendationService = new UserRecommendationService();
export default userRecommendationService;
