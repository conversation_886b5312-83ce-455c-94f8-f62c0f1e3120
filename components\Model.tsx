import React, { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { useGLTF } from '@react-three/drei';
import { Group } from 'three';

interface LocalModelProps {
  modelPath: string;
  scale?: number;
  position?: [number, number, number];
  rotation?: [number, number, number];
}

export function LocalModel({ modelPath, scale = 1, position = [0, 0, 0], rotation = [0, 0, 0] }: LocalModelProps) {
  const groupRef = useRef<Group>(null);
  const [hovered, setHovered] = useState(false);

console.log('🔄 Loading local model from:', modelPath);

  // Load the GLTF model with error handling
  let gltf;
  try {
    gltf = useGLTF(modelPath);
console.log('✅ Local model loaded successfully!');
console.log('📊 Model details:', {
      scene: gltf.scene,
      animations: gltf.animations?.length || 0,
      cameras: gltf.cameras?.length || 0,
      materials: Object.keys(gltf.materials || {}).length,
      nodes: Object.keys(gltf.nodes || {}).length
    });
  } catch (error) {
console.error('❌ Error loading local GLTF model:', error);
    return null;
  }

  // Optional: Add subtle hover effect
  useFrame((state, delta) => {
    if (groupRef.current && hovered) {
      groupRef.current.rotation.y += delta * 0.1;
    }
  });

  return (
    <primitive
      ref={groupRef}
      object={gltf.scene}
      scale={scale}
      position={position}
      rotation={rotation}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    />
  );
}

// Preload the models for better performance (temp.glb first)
useGLTF.preload(require('../assets/models/temp.glb'));

export default LocalModel;
