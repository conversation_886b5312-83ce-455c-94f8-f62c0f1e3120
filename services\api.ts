import AsyncStorage from '@react-native-async-storage/async-storage';

const BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

// API Response Types
export interface User {
  id: string;
  name: string;
  email: string;
  gender: 'male' | 'female';
  createdAt: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: User;
  requiresEmailVerification?: boolean;
}

export interface OTPVerificationResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: User;
}

export interface FaceAnalysis {
  success: boolean;
  message: string;
  data: {
    analysisId: string;
    imageUrl: string;
    faceDetected: boolean;
    processingTime: number;
    confidence: number;
    colors: {
      hairColor: {
        primary: string;
        hex: string;
        rgb: { r: number; g: number; b: number };
        confidence: number;
      };
      skinTone: {
        primary: string;
        hex: string;
        rgb: { r: number; g: number; b: number };
        confidence: number;
      };
      eyeColor: {
        primary: string;
        hex: string;
        rgb: { r: number; g: number; b: number };
        confidence: number;
      };
      lipColor: {
        primary: string;
        hex: string;
        rgb: { r: number; g: number; b: number };
        confidence: number;
      };
    };
    dimensions: {
      faceLength: number;
      faceWidth: number;
      jawWidth: number;
      foreheadWidth: number;
      cheekboneWidth: number;
      lengthToWidthRatio: number;
      jawToForeheadRatio: number;
      cheekboneToJawRatio: number;
    };
    features: {
      faceShape: string;
      eyeShape: string;
      eyeDistance: string;
      eyebrowShape: string;
      noseShape: string;
      lipShape: string;
    };
    imageInfo: {
      originalFileName: string;
      format: string;
      dimensions: { width: number; height: number };
      fileSize: number;
    };
    metadata: {
      errors: string[];
      warnings: string[];
      algorithm: string;
    };
  };
}

export interface ColorRecommendation {
  success: boolean;
  message: string;
  data: {
    recommendationId: string;
    faceAnalysisId: string;
    aiService: string;
    processingTime: number;
    cached: boolean;
    outfits: Array<{
      outfitName: string;
      shirt: {
        color: string;
        hex: string;
        reason: string;
      };
      pants: {
        color: string;
        hex: string;
        reason: string;
      };
      shoes: {
        color: string;
        hex: string;
        reason: string;
      };
      overallReason: string;
    }>;
    colorPalette: {
      bestColors: string[];
      avoidColors: string[];
      seasonalType: string;
    };
    advice: string;
    confidence: number;
    createdAt: string;
  };
}

class ApiService {
  private async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('authToken');
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  async setAuthToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('authToken', token);
      // console.log('Auth token stored successfully'); // REMOVED: Sensitive token info
    } catch (error) {
      console.error('Error setting auth token:', error);
      throw error;
    }
  }

  private async removeAuthToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem('authToken');
    } catch (error) {
      console.error('Error removing auth token:', error);
    }
  }

  private async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<any> {
    const token = await this.getAuthToken();

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
    };

    try {
      // console.log(`API Request: ${options.method || 'GET'} ${BASE_URL}${endpoint}`, {
      //   hasToken: !!token,
      //   headers: config.headers
      // }); // REMOVED: Sensitive API request headers and token info

      const response = await fetch(`${BASE_URL}${endpoint}`, config);
      const data = await response.json();

      // console.log(`API Response: ${response.status}`, {
      //   endpoint,
      //   success: response.ok,
      //   data: endpoint.includes('/auth/') ? { ...data, token: data.token ? '[HIDDEN]' : undefined } : data
      // }); // REMOVED: Sensitive API response data

      if (!response.ok) {
        const errorMessage = data.message || `API request failed with status ${response.status}`;
        console.error(`API Error: ${response.status}`, errorMessage);

        // Handle token expiration specifically
        if (response.status === 401) {
          // console.log('API: 🔐 Token expired or invalid, clearing stored token'); // REMOVED: Sensitive token info
          await this.removeAuthToken();

          // Create a specific token expiration error
          const error = new Error('Token expired or invalid');
          (error as any).code = 'TOKEN_EXPIRED';
          (error as any).status = 401;
          (error as any).userMessage = 'Your session has expired. Please login again.';
          (error as any).funnyMessage = '⏰ Session expire ho gaya! Dobara login kar bhai! 🔄';
          throw error;
        }

        // Handle other HTTP errors
        const error = new Error(errorMessage);
        (error as any).status = response.status;
        (error as any).response = data;
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('API request error:', {
        endpoint,
        error: error.message,
        stack: error.stack
      });

      // If it's already a token expiration error, re-throw it
      if (error.code === 'TOKEN_EXPIRED') {
        throw error;
      }

      // Handle network errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        const networkError = new Error('Network connection failed');
        (networkError as any).code = 'NETWORK_ERROR';
        (networkError as any).userMessage = 'Unable to connect to server. Please check your internet connection.';
        (networkError as any).funnyMessage = '📶 Arre yaar, internet bhi nahi hai! WiFi check kar le! 📡';
        throw networkError;
      }

      throw error;
    }
  }

  // Health Check
  async healthCheck(): Promise<any> {
    return this.makeRequest('/health');
  }

  // Authentication
  async register(userData: {
    name: string;
    email: string;
    password: string;
    gender: 'male' | 'female';
  }): Promise<AuthResponse> {
    // console.log('API: Starting registration for:', { ...userData, password: '[HIDDEN]' }); // REMOVED: Sensitive user data

    const response = await this.makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    // console.log('API: 📥 Registration response received from backend:', {
    //   success: response.success,
    //   hasUser: !!response.user,
    //   hasToken: !!response.token,
    //   message: response.message,
    //   requiresEmailVerification: response.requiresEmailVerification
    // }); // REMOVED: Sensitive registration response info

    // FOR SIGNUP FLOW: Never store token during registration - always require OTP verification
    if (response.success) {
      console.log('API: ✅ Registration successful - forcing OTP verification flow');
      // Always set flag to indicate email verification is needed for signup
      response.requiresEmailVerification = true;
      // console.log('API: 🔧 Set requiresEmailVerification to true (forced for signup flow)'); // REMOVED: Sensitive flow info

      // Don't store any token during registration - user must verify email first
      if (response.token) {
        // console.log('API: 🚫 Ignoring token from registration - OTP verification required first'); // REMOVED: Sensitive token info
      }
    } else {
      console.warn('API: ❌ Registration failed');
    }

    console.log('API: 📤 Final registration response being returned:', {
      success: response.success,
      requiresEmailVerification: response.requiresEmailVerification,
      hasToken: !!response.token,
      hasUser: !!response.user
    });

    return response;
  }

  async login(credentials: {
    email: string;
    password: string;
  }): Promise<AuthResponse> {
    // console.log('API: Starting login for:', credentials.email); // REMOVED: Sensitive email info

    const response = await this.makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    // console.log('API: Login response received:', {
    //   success: response.success,
    //   hasUser: !!response.user,
    //   hasToken: !!response.token,
    //   message: response.message
    // }); // REMOVED: Sensitive login response info

    if (response.success && response.token) {
      // console.log('API: Storing auth token after login'); // REMOVED: Sensitive token info
      await this.setAuthToken(response.token);
    } else {
      console.warn('API: Login response missing token or not successful');
    }

    return response;
  }

  async logout(): Promise<void> {
    await this.removeAuthToken();
  }

  // OTP Email Verification
  async verifyEmailOTP(email: string, otp: string): Promise<OTPVerificationResponse> {
    // console.log('API: Starting OTP verification for:', email); // REMOVED: Sensitive email info

    const response = await this.makeRequest('/auth/verify-email-otp', {
      method: 'POST',
      body: JSON.stringify({ email, otp }),
    });

    console.log('API: OTP verification response received:', {
      success: response.success,
      hasUser: !!response.user,
      hasToken: !!response.token,
      message: response.message
    });

    if (response.success && response.token) {
      // console.log('API: Storing auth token after OTP verification'); // REMOVED: Sensitive token info
      await this.setAuthToken(response.token);
    }

    return response;
  }

  async resendVerificationOTP(email: string): Promise<{ success: boolean; message: string }> {
    // console.log('API: Resending verification OTP for:', email); // REMOVED: Sensitive email info

    const response = await this.makeRequest('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });

    console.log('API: Resend OTP response:', {
      success: response.success,
      message: response.message
    });

    return response;
  }

  async getProfile(bustCache: boolean = false): Promise<User> {
    // Add cache-busting parameter to help with caching issues
    const cacheBuster = bustCache ? `?_t=${Date.now()}` : '';
    const endpoint = `/auth/me${cacheBuster}`;

    console.log('API: Getting profile from:', endpoint, bustCache ? '(cache-busted)' : '(normal)');

    const response = await this.makeRequest(endpoint, {
      // Add cache-busting headers
      headers: bustCache ? {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      } : undefined
    });

    // console.log('API: Profile response:', response); // REMOVED: Sensitive profile response

    // Handle nested response format: { data: { user: {...} } }
    if (response.data && response.data.user) {
      // console.log('API: Profile response has nested user data'); // REMOVED: Sensitive user data info
      return response.data.user;
    }

    // Handle direct user format: { user: {...} }
    if (response.user) {
      // console.log('API: Profile response has direct user data'); // REMOVED: Sensitive user data info
      return response.user;
    }

    // Handle direct format (user data at root level)
    if (response.id) {
      // console.log('API: Profile response is direct user object'); // REMOVED: Sensitive user data info
      return response;
    }

    console.error('API: Unexpected profile response format:', response);
    throw new Error('Invalid profile response format');
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    // console.log('API: Updating profile with data:', userData); // REMOVED: Sensitive user data

    const response = await this.makeRequest('/auth/update-profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });

    // console.log('API: Profile update response:', response); // REMOVED: Sensitive profile response

    // Handle different response formats
    if (response.data && response.data.user) {
      // console.log('API: Profile update response has nested user data'); // REMOVED: Sensitive user data info
      return response.data.user;
    }

    if (response.user) {
      // console.log('API: Profile update response has direct user data'); // REMOVED: Sensitive user data info
      return response.user;
    }

    if (response.id) {
      // console.log('API: Profile update response is direct user object'); // REMOVED: Sensitive user data info
      return response;
    }

    console.error('API: Unexpected profile update response format:', response);
    throw new Error('Invalid profile update response format');
  }

  async changePassword(passwordData: { currentPassword: string; newPassword: string }): Promise<void> {
    // console.log('API: Changing password...'); // REMOVED: Sensitive password operation info

    const response = await this.makeRequest('/auth/change-password', {
      method: 'PUT',
      body: JSON.stringify(passwordData),
    });

    // console.log('API: Password change response:', response); // REMOVED: Sensitive response info
    return response;
  }

  // Face Analysis with Cloudinary direct upload
  async analyzeFace(imageUri: string): Promise<FaceAnalysis> {
    try {
      // console.log('🔍 Starting face analysis with Cloudinary direct upload:', imageUri); // REMOVED: Sensitive image URI

      // Step 1: Get upload signature from backend
      // console.log('📝 Getting upload signature...'); // REMOVED: Debug logging
      const signatureResponse = await this.getUploadSignature();
      if (!signatureResponse.success) {
        throw new Error('Failed to get upload signature');
      }

      // Step 2: Upload image directly to Cloudinary using fixed mobile signature
      // console.log('☁️ Uploading to Cloudinary with mobile signature...'); // REMOVED: Debug logging
      const uploadResult = await this.uploadToCloudinary(imageUri, signatureResponse.data);

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Failed to upload image');
      }

      // Step 3: Analyze face using the uploaded image
      console.log('🔍 Analyzing face from Cloudinary image...');
      const analysisResult = await this.analyzeFaceFromCloudinary(uploadResult.data, imageUri);

      console.log('✅ Face analysis completed successfully');
      return analysisResult;

    } catch (error: any) {
      console.error('❌ Face analysis error:', error);

      // Provide more specific error messages
      if (error.message?.includes('validationResult is not defined')) {
        throw new Error('Backend service error. The face analysis service is experiencing technical difficulties. Please try again in a few minutes.');
      } else if (error.message?.includes('Backend validation error')) {
        throw new Error('Service temporarily unavailable. Please try again later.');
      } else if (error.message?.includes('Network request failed')) {
        throw new Error('Network connection failed. Please check your internet connection and try again.');
      } else if (error.message?.includes('Failed to get upload signature')) {
        throw new Error('Upload service is temporarily unavailable. Please try again later.');
      } else if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        throw new Error('Authentication failed. Please log out and log back in.');
      } else if (error.message?.includes('Invalid transformation') || error.message?.includes('transformation component')) {
        throw new Error('Image processing configuration error. The image was uploaded but may not be optimized. Please try again.');
      } else if (error.message?.includes('Image upload incomplete')) {
        throw new Error('Image upload failed. Please try selecting and uploading the image again.');
      } else {
        throw new Error(error.message || 'Face analysis failed. Please try again.');
      }
    }
  }

  // Get upload configuration from backend
  private async getUploadConfig(): Promise<any> {
    console.log('⚙️ Getting upload configuration...');

    const token = await this.getAuthToken();
    const response = await fetch(`${BASE_URL}/upload/config`, {
      method: 'GET',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    const data = await response.json();
    console.log('⚙️ Upload config response:', data);

    if (!response.ok) {
      throw new Error(data.message || 'Failed to get upload configuration');
    }

    return data;
  }

  // Get upload signature from new mobile endpoint (fixed backend)
  private async getUploadSignature(): Promise<any> {
    console.log('📝 Requesting mobile upload signature...');

    const token = await this.getAuthToken();
    const response = await fetch(`${BASE_URL}/upload/mobile-signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    const data = await response.json();
    console.log('📝 Mobile signature response:', data);

    if (!response.ok) {
      throw new Error(data.message || 'Failed to get mobile upload signature');
    }

    return { success: true, data }; // Wrap in success format for consistency
  }

  // Upload image directly to Cloudinary
  private async uploadToCloudinary(imageUri: string, signatureData: any): Promise<any> {
    try {
      console.log('☁️ Uploading to Cloudinary with signature data...');
      console.log('☁️ Received signature data:', JSON.stringify(signatureData, null, 2));

      // New mobile signature format - parameters are directly in signatureData
      const requiredParams = ['public_id', 'timestamp', 'signature', 'api_key', 'upload_url'];
      const missingParams = requiredParams.filter(param => !signatureData[param]);

      if (missingParams.length > 0) {
        throw new Error(`Missing required parameters for Cloudinary upload: ${missingParams.join(', ')}`);
      }

      console.log('☁️ Mobile signature validation - all required params present');
      console.log('☁️ Upload URL:', signatureData.upload_url);
      console.log('☁️ Public ID:', signatureData.public_id);
      console.log('☁️ Timestamp:', signatureData.timestamp);
      console.log('☁️ API Key:', signatureData.api_key);
      console.log('☁️ Signature:', signatureData.signature);
      console.log('☁️ Transformation:', signatureData.transformation);

      // Create FormData for Cloudinary upload
      const uploadFormData = new FormData();

      console.log('☁️ Adding mobile signature parameters...');

      // Add all signature parameters in the exact format from mobile endpoint
      const signatureParams = [
        'signature',
        'timestamp',
        'public_id',
        'api_key',
        'folder',
        'tags',
        'context',
        'transformation',
        'allowed_formats'
      ];

      signatureParams.forEach(param => {
        if (signatureData[param] !== undefined) {
          console.log(`☁️ Adding param: ${param} = ${signatureData[param]}`);
          uploadFormData.append(param, signatureData[param]);
        }
      });

      // Add the image file last
      uploadFormData.append('file', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'face-image.jpg',
      } as any);

      console.log('☁️ Sending upload request to Cloudinary:', signatureData.upload_url);

      // Upload directly to Cloudinary (no auth headers needed)
      const response = await fetch(signatureData.upload_url, {
        method: 'POST',
        body: uploadFormData,
      });

      console.log('☁️ Cloudinary response status:', response.status);

      const uploadResult = await response.json();
      console.log('☁️ Cloudinary upload response:', uploadResult);

      if (uploadResult.error) {
        console.error('❌ Cloudinary error details:', uploadResult.error);

        // Provide specific error handling for signature issues
        if (uploadResult.error.message?.includes('Invalid Signature')) {
          throw new Error(`Cloudinary signature validation failed. This usually means the upload parameters were modified. Original error: ${uploadResult.error.message}`);
        }

        throw new Error(uploadResult.error.message || 'Cloudinary upload failed');
      }

      if (!response.ok) {
        throw new Error(`Cloudinary upload failed with status ${response.status}`);
      }

      return {
        success: true,
        data: {
          publicId: uploadResult.public_id,
          url: uploadResult.secure_url,
          width: uploadResult.width,
          height: uploadResult.height,
          format: uploadResult.format,
          bytes: uploadResult.bytes,
          version: uploadResult.version,
          signature: uploadResult.signature,
          timestamp: uploadResult.created_at
        }
      };

    } catch (error: any) {
      console.error('❌ Cloudinary upload error:', error);
      return {
        success: false,
        error: error.message || 'Failed to upload to Cloudinary'
      };
    }
  }

  // Get upload signature without transformation parameter
  private async getUploadSignatureWithoutTransformation(): Promise<any> {
    console.log('📝 Requesting upload signature without transformation...');

    const token = await this.getAuthToken();
    const response = await fetch(`${BASE_URL}/upload/signature`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: JSON.stringify({
        skipTransformation: true // Request signature without transformation
      }),
    });

    const data = await response.json();
    console.log('📝 Upload signature (no transformation) response:', data);

    if (!response.ok) {
      throw new Error(data.message || 'Failed to get upload signature without transformation');
    }

    return data;
  }

  // Fallback upload method without transformation parameter
  private async uploadToCloudinaryWithoutTransformation(imageUri: string, originalSignatureData: any): Promise<any> {
    try {
      console.log('☁️ Fallback upload: Getting new signature without transformation...');

      // Try to get a new signature without transformation first
      let signatureData;
      try {
        const newSignatureResponse = await this.getUploadSignatureWithoutTransformation();
        if (newSignatureResponse.success) {
          signatureData = newSignatureResponse.data;
          console.log('✅ Got new signature without transformation');
        } else {
          throw new Error('Failed to get new signature');
        }
      } catch (error) {
        console.log('⚠️ Could not get new signature, using original signature without transformation parameter');
        signatureData = originalSignatureData;
      }

      const { formData, uploadUrl } = signatureData;

      // Create FormData for Cloudinary upload
      const uploadFormData = new FormData();

      console.log('☁️ Adding parameters without transformation...');

      // Add all parameters except transformation
      Object.keys(formData).forEach(key => {
        if (key === 'transformation') {
          console.log('⚠️ Skipping transformation parameter in fallback upload');
          return;
        }

        const value = formData[key];
        console.log(`☁️ Adding param: ${key} = ${value}`);
        uploadFormData.append(key, value);
      });

      // Add the image file
      uploadFormData.append('file', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'face-image.jpg',
      } as any);

      console.log('☁️ Sending fallback upload request to Cloudinary:', uploadUrl);

      // Upload directly to Cloudinary
      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: uploadFormData,
      });

      console.log('☁️ Fallback upload response status:', response.status);

      const uploadResult = await response.json();
      console.log('☁️ Fallback upload response:', uploadResult);

      if (uploadResult.error) {
        console.error('❌ Fallback upload error:', uploadResult.error);
        return {
          success: false,
          error: uploadResult.error.message || 'Fallback upload failed'
        };
      }

      if (!response.ok) {
        return {
          success: false,
          error: `Fallback upload failed with status ${response.status}`
        };
      }

      return {
        success: true,
        data: {
          publicId: uploadResult.public_id,
          url: uploadResult.secure_url,
          width: uploadResult.width,
          height: uploadResult.height,
          format: uploadResult.format,
          bytes: uploadResult.bytes,
          version: uploadResult.version,
          signature: uploadResult.signature,
          timestamp: uploadResult.created_at
        }
      };

    } catch (error: any) {
      console.error('❌ Fallback upload error:', error);
      return {
        success: false,
        error: error.message || 'Fallback upload failed'
      };
    }
  }

  // Convert JSON transformation parameter to Cloudinary format
  private convertTransformationParameter(transformationValue: string): string | null {
    try {
      console.log('🔄 Converting transformation parameter:', transformationValue);

      // Handle different transformation formats
      if (typeof transformationValue === 'string') {
        // If it's already in Cloudinary format (e.g., "q_auto,f_auto"), return as-is
        if (!transformationValue.startsWith('[') && !transformationValue.startsWith('{')) {
          console.log('✅ Transformation already in Cloudinary format');
          return transformationValue;
        }

        // Parse JSON transformation array
        let transformations;
        try {
          transformations = JSON.parse(transformationValue);
        } catch (parseError) {
          console.error('❌ Failed to parse transformation JSON:', parseError);
          return null;
        }

        // Handle array of transformations
        if (Array.isArray(transformations) && transformations.length > 0) {
          const transform = transformations[0]; // Use first transformation
          const parts: string[] = [];

          // Convert each transformation property to Cloudinary format
          if (transform.quality) {
            // Keep the colon in quality values (e.g., "auto:good" stays "auto:good")
            parts.push(`q_${transform.quality}`);
          }
          if (transform.fetch_format) {
            parts.push(`f_${transform.fetch_format}`);
          }
          if (transform.width) {
            parts.push(`w_${transform.width}`);
          }
          if (transform.height) {
            parts.push(`h_${transform.height}`);
          }
          if (transform.crop) {
            parts.push(`c_${transform.crop}`);
          }

          const result = parts.join(',');
          console.log('✅ Converted JSON to Cloudinary format:', result);
          console.log('🔍 Transformation breakdown:', parts);
          return result;
        }

        // Handle single transformation object
        if (typeof transformations === 'object' && transformations !== null) {
          const parts: string[] = [];

          if (transformations.quality) {
            // Keep the colon in quality values (e.g., "auto:good" stays "auto:good")
            parts.push(`q_${transformations.quality}`);
          }
          if (transformations.fetch_format) {
            parts.push(`f_${transformations.fetch_format}`);
          }
          if (transformations.width) {
            parts.push(`w_${transformations.width}`);
          }
          if (transformations.height) {
            parts.push(`h_${transformations.height}`);
          }
          if (transformations.crop) {
            parts.push(`c_${transformations.crop}`);
          }

          const result = parts.join(',');
          console.log('✅ Converted object to Cloudinary format:', result);
          return result;
        }
      }

      console.log('⚠️ Unrecognized transformation format');
      return null;

    } catch (error) {
      console.error('❌ Error converting transformation parameter:', error);
      return null;
    }
  }

  // Analyze face using the new /face/analyze-url endpoint
  private async analyzeFaceFromCloudinary(uploadData: any, originalImageUri: string): Promise<FaceAnalysis> {
    try {
      console.log('🔍 Analyzing face using /face/analyze-url endpoint...');
      console.log('🔍 Upload data received:', JSON.stringify(uploadData, null, 2));

      // Validate required upload data
      if (!uploadData) {
        throw new Error('Upload data is missing');
      }

      if (!uploadData.publicId) {
        throw new Error('Public ID is missing from upload data');
      }

      if (!uploadData.url) {
        throw new Error('Image URL is missing from upload data');
      }

      const token = await this.getAuthToken();
      if (!token) {
        throw new Error('Authentication token is missing');
      }

      // Get original filename from URI with better validation
      const originalFileName = originalImageUri?.split('/').pop() || 'face-image.jpg';

      // Create JSON payload for the /face/analyze-url endpoint (RECOMMENDED for mobile)
      const requestBody = {
        imageUrl: uploadData.url,
        originalFileName: originalFileName || 'face.jpg'
        // Only send required fields as per documentation
      };

      console.log('🔍 Sending face analysis request to /face/analyze-url with JSON');
      console.log('🔍 Image URL:', uploadData.url);
      console.log('🔍 Public ID:', uploadData.publicId);
      console.log('🔍 Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(`${BASE_URL}/face/analyze-url`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
      });

      console.log('🔍 Face analysis response status:', response.status);

      const data = await response.json();
      console.log('🔍 Face analysis response:', data);

      // Extract analysis ID - backend returns it as data._id
      const analysisId = data?.data?._id || data?._id;
      console.log('🔍 Analysis ID in response:', analysisId || 'NOT FOUND');
      console.log('🔍 Response structure:', {
        hasData: !!data.data,
        hasAnalysisId: !!analysisId,
        dataKeys: data?.data ? Object.keys(data.data) : 'no data object',
        topLevelKeys: Object.keys(data)
      });

      if (!response.ok) {
        // Handle specific backend validation errors
        if (data.message?.includes('validationResult is not defined')) {
          throw new Error('Backend validation error. Please try again or contact support if the issue persists.');
        }

        throw new Error(data.message || `Face analysis failed with status ${response.status}`);
      }

      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format from face analysis service');
      }

      // Check if analysis ID is missing and log detailed info
      if (!analysisId) {
        console.warn('⚠️ MISSING ANALYSIS ID in backend response!');
        console.warn('⚠️ Backend should return _id field for color recommendations');
        console.warn('⚠️ Full response structure:', JSON.stringify(data, null, 2));
      } else {
        console.log('✅ Analysis ID found:', analysisId);
      }

      return data;

    } catch (error: any) {
      console.error('❌ Face analysis from Cloudinary error:', error);

      // Provide more specific error messages
      if (error.message?.includes('validationResult is not defined')) {
        throw new Error('Backend service error. The face analysis service is experiencing technical difficulties. Please try again later.');
      } else if (error.message?.includes('Authentication token is missing')) {
        throw new Error('Authentication required. Please log out and log back in.');
      } else if (error.message?.includes('Public ID is missing') || error.message?.includes('Image URL is missing')) {
        throw new Error('Image upload incomplete. Please try uploading the image again.');
      } else if (error.message?.includes('Invalid URL') || error.message?.includes('URL format')) {
        throw new Error('Invalid image URL format. Please try uploading the image again.');
      } else if (error.message?.includes('Failed to fetch') || error.message?.includes('Network request failed')) {
        throw new Error('Network error. Please check your internet connection and try again.');
      }

      throw new Error(error.message || 'Failed to analyze face from uploaded image');
    }
  }

  async getFaceAnalysisHistory(): Promise<FaceAnalysis[]> {
    // console.log('API: Getting face analysis history'); // REMOVED: Debug logging
    const response = await this.makeRequest('/face/history');

    // Handle the nested response structure
    if (response && response.data && response.data.analyses) {
      // console.log('API: Found', response.data.analyses.length, 'analyses in history'); // REMOVED: Debug logging
      // console.log('API: Sample analysis data:', JSON.stringify(response.data.analyses[0], null, 2)); // REMOVED: Sensitive analysis data

      // Transform the response to match the expected FaceAnalysis interface
      const transformedData = response.data.analyses
        .filter((analysis: any) => {
          // Only include analyses that have color data (complete analyses)
          return analysis.colors && Object.keys(analysis.colors).length > 0;
        })
        .map((analysis: any) => {
          console.log('API: Transforming analysis:', analysis._id);
          console.log('API: Original colors:', JSON.stringify(analysis.colors, null, 2));

          const transformed = {
            success: true,
            message: 'Analysis retrieved from history',
            data: {
              analysisId: analysis._id,
              imageUrl: analysis.imageUrl,
              faceDetected: analysis.faceDetected,
              processingTime: analysis.analysisMetadata?.processingTime || 0,
              confidence: analysis.analysisMetadata?.confidence || 0,
              colors: {
                hairColor: analysis.colors?.hairColor ? {
                  primary: analysis.colors.hairColor.primary || 'unknown',
                  hex: analysis.colors.hairColor.hex || '#000000',
                  rgb: analysis.colors.hairColor.rgb || { r: 0, g: 0, b: 0 },
                  confidence: analysis.colors.hairColor.confidence || 0
                } : { primary: 'unknown', hex: '#000000', rgb: { r: 0, g: 0, b: 0 }, confidence: 0 },
                skinTone: analysis.colors?.skinTone ? {
                  primary: analysis.colors.skinTone.primary || 'unknown',
                  hex: analysis.colors.skinTone.hex || '#000000',
                  rgb: analysis.colors.skinTone.rgb || { r: 0, g: 0, b: 0 },
                  confidence: analysis.colors.skinTone.confidence || 0
                } : { primary: 'unknown', hex: '#000000', rgb: { r: 0, g: 0, b: 0 }, confidence: 0 },
                eyeColor: analysis.colors?.eyeColor ? {
                  primary: analysis.colors.eyeColor.primary || 'unknown',
                  hex: analysis.colors.eyeColor.hex || '#000000',
                  rgb: analysis.colors.eyeColor.rgb || { r: 0, g: 0, b: 0 },
                  confidence: analysis.colors.eyeColor.confidence || 0
                } : { primary: 'unknown', hex: '#000000', rgb: { r: 0, g: 0, b: 0 }, confidence: 0 },
                lipColor: analysis.colors?.lipColor ? {
                  primary: analysis.colors.lipColor.primary || 'unknown',
                  hex: analysis.colors.lipColor.hex || '#000000',
                  rgb: analysis.colors.lipColor.rgb || { r: 0, g: 0, b: 0 },
                  confidence: analysis.colors.lipColor.confidence || 0
                } : { primary: 'unknown', hex: '#000000', rgb: { r: 0, g: 0, b: 0 }, confidence: 0 },
              },
              dimensions: analysis.faceDimensions ? {
                faceLength: analysis.faceDimensions.faceLength || 0,
                faceWidth: analysis.faceDimensions.faceWidth || 0,
                jawWidth: analysis.faceDimensions.jawWidth || 0,
                foreheadWidth: analysis.faceDimensions.foreheadWidth || 0,
                cheekboneWidth: analysis.faceDimensions.cheekboneWidth || 0,
                lengthToWidthRatio: analysis.faceDimensions.lengthToWidthRatio || 0,
                jawToForeheadRatio: analysis.faceDimensions.jawToForeheadRatio || 0,
                cheekboneToJawRatio: analysis.faceDimensions.cheekboneToJawRatio || 0,
              } : {
                faceLength: 0,
                faceWidth: 0,
                jawWidth: 0,
                foreheadWidth: 0,
                cheekboneWidth: 0,
                lengthToWidthRatio: 0,
                jawToForeheadRatio: 0,
                cheekboneToJawRatio: 0,
              },
              features: analysis.facialFeatures ? {
                faceShape: analysis.facialFeatures.faceShape || 'unknown',
                eyeShape: analysis.facialFeatures.eyeShape || 'unknown',
                eyeDistance: analysis.facialFeatures.eyeDistance || 'unknown',
                eyebrowShape: analysis.facialFeatures.eyebrowShape || 'unknown',
                noseShape: analysis.facialFeatures.noseShape || 'unknown',
                lipShape: analysis.facialFeatures.lipShape || 'unknown',
              } : {
                faceShape: 'unknown',
                eyeShape: 'unknown',
                eyeDistance: 'unknown',
                eyebrowShape: 'unknown',
                noseShape: 'unknown',
                lipShape: 'unknown',
              },
              imageInfo: {
                originalFileName: analysis.originalFileName || 'Unknown',
                format: analysis.imageFormat || 'unknown',
                dimensions: analysis.imageDimensions || { width: 0, height: 0 },
                fileSize: analysis.fileSize || 0,
              },
              metadata: {
                errors: [],
                warnings: analysis.analysisMetadata?.warnings || [],
                algorithm: analysis.analysisMetadata?.algorithm || 'unknown',
              },
            },
          };

          // console.log('API: Transformed colors:', JSON.stringify(transformed.data.colors, null, 2)); // REMOVED: Sensitive color data
          return transformed;
        });

      // console.log('API: Returning', transformedData.length, 'transformed analyses'); // REMOVED: Debug logging
      return transformedData;
    }

    // console.log('API: No analyses found in response'); // REMOVED: Debug logging
    return [];
  }

  async getFaceAnalysis(id: string): Promise<FaceAnalysis> {
    return this.makeRequest(`/face/analysis/${id}`);
  }

  async getColorPalette(id: string): Promise<string[]> {
    console.log('API: Getting color palette for analysis:', id);
    try {
      const response = await this.makeRequest(`/face/analysis/${id}/colors`);

      // If the response has colors data, extract hex values
      if (response && response.data && response.data.colors) {
        const colors = response.data.colors;
        return [
          colors.hairColor?.hex,
          colors.skinTone?.hex,
          colors.eyeColor?.hex,
          colors.lipColor?.hex,
        ].filter(color => color && color !== '#000000'); // Filter out null/default colors
      }

      // If direct array response
      if (Array.isArray(response)) {
        return response;
      }

      return [];
    } catch (error) {
      console.log('API: Color palette not available for analysis:', id);
      return [];
    }
  }

  async getFaceMeasurements(id: string): Promise<any> {
    console.log('API: Getting face measurements for analysis:', id);
    try {
      const response = await this.makeRequest(`/face/analysis/${id}/measurements`);

      // Handle nested response structure
      if (response && response.data) {
        return response.data;
      }

      return response;
    } catch (error) {
      console.log('API: Face measurements not available for analysis:', id);
      return null;
    }
  }

  // Change password (duplicate method removed - using the one above)



  // Get color recommendations based on face analysis
  async getColorRecommendations(analysisId: string): Promise<ColorRecommendation> {
    console.log('API: Getting color recommendations for analysis:', analysisId);

    // Call the endpoint with preferences as per documentation
    const endpoint = `/face/analysis/${analysisId}/recommendations`;
    console.log('API: Calling POST endpoint:', `${BASE_URL}${endpoint}`);

    const requestBody = {
      preferences: {
        style: 'casual',
        occasion: 'everyday',
        season: 'spring'
      }
    };

    const response = await this.makeRequest(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('API: Color recommendations response:', {
      success: response.success,
      hasOutfits: !!response.data?.outfits,
      outfitCount: response.data?.outfits?.length || 0
    });

    return response;
  }

  // Get face analysis history for the current user
  async getFaceAnalysisHistory(): Promise<any> {
    console.log('API: Getting face analysis history...');

    const response = await this.makeRequest('/face/history', {
      method: 'GET',
    });

    console.log('API: Face analysis history response:', {
      success: response.success,
      count: response.data?.count || 0,
      analysesCount: response.data?.analyses?.length || 0
    });

    return response;
  }

  // Get specific face analysis by ID
  async getFaceAnalysisById(analysisId: string): Promise<any> {
    console.log('API: Getting face analysis by ID:', analysisId);

    const response = await this.makeRequest(`/face/analysis/${analysisId}`, {
      method: 'GET',
    });

    console.log('API: Face analysis by ID response:', {
      success: response.success,
      hasData: !!response.data,
      analysisId: response.data?._id
    });

    return response;
  }

  // Get color recommendation history for the current user
  async getColorRecommendationHistory(limit: number = 10): Promise<any> {
    console.log('API: Getting color recommendation history...');

    const response = await this.makeRequest(`/face/recommendations/history?limit=${limit}`, {
      method: 'GET',
    });

    console.log('API: Color recommendation history response:', {
      success: response.success,
      count: response.data?.count || 0,
      recommendationsCount: response.data?.recommendations?.length || 0
    });

    return response;
  }

  // Delete a specific face analysis
  async deleteFaceAnalysis(analysisId: string): Promise<any> {
    console.log('API: Deleting face analysis:', analysisId);

    const response = await this.makeRequest(`/face/analysis/${analysisId}`, {
      method: 'DELETE',
    });

    console.log('API: Delete face analysis response:', {
      success: response.success,
      analysisId: analysisId
    });

    return response;
  }

  // Get detailed color palette for specific analysis
  async getDetailedColorPalette(analysisId: string): Promise<any> {
    console.log('API: Getting detailed color palette for analysis:', analysisId);

    const response = await this.makeRequest(`/face/analysis/${analysisId}/colors`, {
      method: 'GET',
    });

    console.log('API: Detailed color palette response:', {
      success: response.success,
      hasColors: !!response.data?.colors,
      hasColorHarmony: !!response.data?.colorHarmony
    });

    return response;
  }

  // Get detailed face measurements for specific analysis
  async getDetailedFaceMeasurements(analysisId: string): Promise<any> {
    console.log('API: Getting detailed face measurements for analysis:', analysisId);

    const response = await this.makeRequest(`/face/analysis/${analysisId}/measurements`, {
      method: 'GET',
    });

    console.log('API: Detailed face measurements response:', {
      success: response.success,
      hasFaceDimensions: !!response.data?.faceDimensions,
      hasFacialFeatures: !!response.data?.facialFeatures,
      hasProportions: !!response.data?.proportions
    });

    return response;
  }

  async deleteFaceAnalysis(id: string): Promise<void> {
    console.log('API: Deleting face analysis:', id);
    return this.makeRequest(`/face/analysis/${id}`, {
      method: 'DELETE',
    });
  }

  async testFaceAnalysis(): Promise<any> {
    return this.makeRequest('/face/test');
  }

  // Check if face analysis service is available
  async checkFaceAnalysisService(): Promise<boolean> {
    try {
      // console.log('🔍 Checking face analysis service health...'); // REMOVED: Debug logging
      const response = await this.testFaceAnalysis();
      // console.log('✅ Face analysis service is available'); // REMOVED: Debug logging
      return true;
    } catch (error) {
      console.error('❌ Face analysis service is not available:', error);
      return false;
    }
  }

  // Performance Metrics
  async getMetrics(): Promise<any> {
    return this.makeRequest('/metrics');
  }

  // Cache Status
  async getCacheStatus(): Promise<any> {
    return this.makeRequest('/cache/status');
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    const token = await this.getAuthToken();
    // console.log('API: Checking authentication, token exists:', !!token); // REMOVED: Sensitive token info
    return !!token;
  }

  // Validate token by making a test request
  async validateToken(): Promise<boolean> {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        // console.log('API: No token found for validation'); // REMOVED: Sensitive token info
        return false;
      }

      // console.log('API: Validating token...'); // REMOVED: Sensitive token info
      await this.getProfile();
      // console.log('API: Token validation successful'); // REMOVED: Sensitive token info
      return true;
    } catch (error: any) {
      // console.log('API: Token validation failed:', error.message); // REMOVED: Sensitive token info
      if (error.message?.includes('Not authorized') || error.message?.includes('401')) {
        // console.log('API: Removing invalid token'); // REMOVED: Sensitive token info
        await this.removeAuthToken();
      }
      return false;
    }
  }
}

export const apiService = new ApiService();
export default apiService;
