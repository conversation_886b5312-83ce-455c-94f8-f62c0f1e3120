import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { apiService } from '../../services/api';

const { width, height } = Dimensions.get('window');

interface OTPVerificationScreenProps {
  email: string;
  onVerificationSuccess: () => void;
  onBackToRegister: () => void;
}

export const OTPVerificationScreen: React.FC<OTPVerificationScreenProps> = ({
  email,
  onVerificationSuccess,
  onBackToRegister,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  // Start resend timer
  useEffect(() => {
    setResendTimer(60); // 60 seconds cooldown
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) {
      // Handle paste
      const pastedOtp = value.slice(0, 6).split('');
      const newOtp = [...otp];
      pastedOtp.forEach((digit, i) => {
        if (i < 6) newOtp[i] = digit;
      });
      setOtp(newOtp);
      
      // Focus on the last filled input or next empty one
      const nextIndex = Math.min(pastedOtp.length, 5);
      inputRefs.current[nextIndex]?.focus();
      return;
    }

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = async () => {
    const otpString = otp.join('').trim();

    // Enhanced validation
    if (!otpString || otpString.length === 0) {
      Alert.alert('OTP Required ⚠️', '📝 OTP daal! Code enter kar! 🔢');
      inputRefs.current[0]?.focus();
      return;
    }

    if (otpString.length !== 6) {
      Alert.alert(
        'Incomplete OTP ⚠️',
        `📝 Pura 6-digit code daal! Abhi ${otpString.length} digits hai! 🔢`,
        [{ text: 'OK 👍', onPress: () => inputRefs.current[otpString.length]?.focus() }]
      );
      return;
    }

    // Check if OTP contains only numbers
    if (!/^\d{6}$/.test(otpString)) {
      Alert.alert(
        'Invalid OTP Format ❌',
        '🔢 Sirf numbers daal! Letters nahi! 📱',
        [{
          text: 'OK 👍',
          onPress: () => {
            setOtp(['', '', '', '', '', '']);
            inputRefs.current[0]?.focus();
          }
        }]
      );
      return;
    }

    try {
      setIsLoading(true);
console.log('OTPVerification: Verifying OTP for:', email);

      // Safety check for API service
      if (!apiService || typeof apiService.verifyEmailOTP !== 'function') {
        throw new Error('API service not available. Please restart the app.');
      }

      // Call API directly instead of using AuthContext (since we don't want auto-login)
      const response = await apiService.verifyEmailOTP(email, otpString);

      if (response.success) {
console.log('OTPVerification: Verification successful');
        Alert.alert(
          'Email Verified! 🎉',
          '✅ Email verify ho gaya! Ab login kar ke app use kar! 🚀\n\nNext step: Login with your email and password',
          [
            {
              text: 'Go to Login 🔑',
              onPress: () => onVerificationSuccess(),
              style: 'default'
            }
          ]
        );
      } else {
        throw new Error(response.message || 'Verification failed');
      }
    } catch (error: any) {
console.error('OTPVerification: Verification error:', error);

      // Enhanced error handling with Hindi-English mix for better UX
      let errorMessage = '❌ Kuch galat ho gaya! Try again kar! 🔄';
      let errorTitle = 'OTP Verification Failed';

      if (error.message?.includes('expired') || error.message?.includes('expire')) {
        errorTitle = 'OTP Expired ⏰';
        errorMessage = '⏰ OTP expire ho gaya! Naya code mangwa! 🔄';
      } else if (error.message?.includes('invalid') || error.message?.includes('incorrect') || error.message?.includes('wrong')) {
        errorTitle = 'Wrong OTP ❌';
        errorMessage = '❌ Galat OTP dala hai! Code check kar ke phir se try kar! 🔍';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorTitle = 'Network Error 📶';
        errorMessage = '📶 Internet connection check kar! Network issue hai! 🌐';
      } else if (error.message?.includes('server') || error.message?.includes('500')) {
        errorTitle = 'Server Error 🔧';
        errorMessage = '🔧 Server mein problem hai! Thoda wait kar ke try kar! ⏳';
      } else if (error.message) {
        errorMessage = `❌ ${error.message} 🔄`;
      }

      Alert.alert(
        errorTitle,
        errorMessage,
        [
          {
            text: 'Try Again 🔄',
            style: 'default',
            onPress: () => {
              // Clear OTP and focus first input
              setOtp(['', '', '', '', '', '']);
              setTimeout(() => {
                inputRefs.current[0]?.focus();
              }, 100);
            }
          }
        ]
      );

      // Clear OTP on error
      setOtp(['', '', '', '', '', '']);
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendTimer > 0) return;

    try {
      setIsResending(true);
console.log('OTPVerification: Resending OTP for:', email);

      // Safety check for API service
      if (!apiService || typeof apiService.resendVerificationOTP !== 'function') {
        throw new Error('API service not available. Please restart the app.');
      }

      const response = await apiService.resendVerificationOTP(email);

      if (response.success) {
        Alert.alert(
          'OTP Sent! 📧',
          `✅ Naya OTP bhej diya! ${email} pe check kar! 📱\n\nCode 6 digits ka hoga.`,
          [{ text: 'OK 👍' }]
        );
        
        // Restart timer
        setResendTimer(60);
        const timer = setInterval(() => {
          setResendTimer((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        throw new Error(response.message || 'Failed to resend OTP');
      }
    } catch (error: any) {
console.error('OTPVerification: Resend error:', error);

      // Enhanced resend error handling
      let errorMessage = '❌ OTP resend nahi ho saka! Try again kar! 🔄';
      let errorTitle = 'Resend Failed';

      if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorTitle = 'Network Error 📶';
        errorMessage = '📶 Internet connection check kar! Network issue hai! 🌐';
      } else if (error.message?.includes('server') || error.message?.includes('500')) {
        errorTitle = 'Server Error 🔧';
        errorMessage = '🔧 Server busy hai! Thoda wait kar ke try kar! ⏳';
      } else if (error.message?.includes('limit') || error.message?.includes('too many')) {
        errorTitle = 'Too Many Requests ⏰';
        errorMessage = '⏰ Bahut zyada requests! Thoda wait kar! 🛑';
      } else if (error.message) {
        errorMessage = `❌ ${error.message} 🔄`;
      }

      Alert.alert(
        errorTitle,
        errorMessage,
        [{ text: 'OK 👍', style: 'default' }]
      );
    } finally {
      setIsResending(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.content}>
        <TouchableOpacity style={styles.backButton} onPress={onBackToRegister}>
          <Ionicons name="arrow-back" size={24} color="#1e293b" />
        </TouchableOpacity>

        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name="mail-unread" size={80} color="#2563eb" />
          </View>
          <Text style={styles.title}>Verify Your Email</Text>
          <Text style={styles.subtitle}>
            We've sent a 6-digit code to{'\n'}
            <Text style={styles.email}>{email}</Text>
          </Text>
        </View>

          <View style={styles.otpContainer}>
            <Text style={styles.otpLabel}>Enter verification code</Text>
            <View style={styles.otpInputContainer}>
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => {
                    if (ref) {
                      inputRefs.current[index] = ref;
                    }
                  }}
                  style={[
                    styles.otpInput,
                    digit ? styles.otpInputFilled : null,
                  ]}
                  value={digit}
                  onChangeText={(value) => handleOtpChange(value, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  keyboardType="numeric"
                  maxLength={6} // Allow paste
                  textAlign="center"
                  selectTextOnFocus
                />
              ))}
            </View>
          </View>

          <TouchableOpacity
            style={[styles.verifyButton, isLoading && styles.disabledButton]}
            onPress={handleVerifyOTP}
            disabled={isLoading}
          >
            <View style={styles.verifyButtonContent}>
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Ionicons name="checkmark-circle" size={20} color="#fff" />
                  <Text style={styles.verifyButtonText}>Verify Email</Text>
                </>
              )}
            </View>
          </TouchableOpacity>

          <View style={styles.resendContainer}>
            <Text style={styles.resendText}>Didn't receive the code?</Text>
            <TouchableOpacity
              style={[styles.resendButton, (resendTimer > 0 || isResending) && styles.disabledButton]}
              onPress={handleResendOTP}
              disabled={resendTimer > 0 || isResending}
            >
              {isResending ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.resendButtonText}>
                  {resendTimer > 0 ? `Resend in ${resendTimer}s` : 'Resend Code'}
                </Text>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.helpContainer}>
            <Text style={styles.helpText}>
              💡 Check your spam folder if you don't see the email
            </Text>
            <Text style={styles.helpText}>
              ⏰ Code expires in 15 minutes
            </Text>
          </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 24,
    zIndex: 1,
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f1f5f9',
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  email: {
    fontWeight: '600',
    color: '#2563eb',
  },
  otpContainer: {
    marginBottom: 32,
  },
  otpLabel: {
    fontSize: 16,
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: '600',
  },
  otpInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
  },
  otpInput: {
    width: 48,
    height: 56,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    backgroundColor: '#f8fafc',
    color: '#1e293b',
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
  },
  otpInputFilled: {
    borderColor: '#2563eb',
    backgroundColor: '#eff6ff',
  },
  verifyButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 24,
  },
  verifyButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  verifyButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  disabledButton: {
    opacity: 0.6,
    backgroundColor: '#94a3b8',
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  resendText: {
    color: '#64748b',
    fontSize: 14,
    marginBottom: 12,
  },
  resendButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  resendButtonText: {
    color: '#2563eb',
    fontSize: 16,
    fontWeight: '600',
  },
  helpContainer: {
    alignItems: 'center',
  },
  helpText: {
    color: '#64748b',
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 8,
  },
});
