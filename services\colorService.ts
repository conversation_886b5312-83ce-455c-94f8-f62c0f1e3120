import { Color } from 'three';

export interface ColorCombination {
  id?: string;
  name?: string;
  shirt: string;
  pants: string;
  shoes: string;
  description?: string;
  confidence?: number;
  source?: 'gemini' | 'default';
  isRecommended?: boolean; // Indicates if this combination is from user recommendations
}

export interface ModelColors {
  hairColor: string;
  shirtColor: string;
  pantsColor: string;
  shoesColor: string;
}

export class ColorService {
  // Convert hex color to Three.js Color
  static hexToColor(hex: string): Color {
    return new Color(hex);
  }

  // Get default color combinations for testing
  static getDefaultCombinations(): ColorCombination[] {
    return [
      {
        id: '1',
        shirt: '#3498db', // Blue
        pants: '#2c3e50', // Dark blue
        shoes: '#000000', // Black
        description: 'Classic blue combination for professional look',
        confidence: 0.95,
      },
      {
        id: '2',
        shirt: '#e74c3c', // Red
        pants: '#34495e', // Dark gray
        shoes: '#8b4513', // Brown
        description: 'Bold red shirt with neutral tones',
        confidence: 0.88,
      },
      {
        id: '3',
        shirt: '#2ecc71', // Green
        pants: '#95a5a6', // Light gray
        shoes: '#000000', // Black
        description: 'Fresh green with light gray pants',
        confidence: 0.82,
      },
    ];
  }

  // Apply colors to male model meshes with dynamic colors
  static applyMaleModelColors(materials: any, combination: ColorCombination, modelColors?: ModelColors) {
    if (!materials) return;

    try {
      const colors = modelColors || {
        hairColor: '#4a2c17',
        shirtColor: combination.shirt,
        pantsColor: combination.pants,
        shoesColor: combination.shoes,
      };

console.log('Applying male model colors:', colors);

      // Apply hair color from user's face analysis
      if (materials['ch31_hair']) {
        materials['ch31_hair'].color = this.hexToColor(colors.hairColor);
console.log('   → Applied hair color:', colors.hairColor);
      }

      // Apply shirt/sweater color from combination
      if (materials['ch31_shirt']) {
        materials['ch31_shirt'].color = this.hexToColor(colors.shirtColor);
console.log('   → Applied shirt color:', colors.shirtColor);
      }
      if (materials['ch31_tshirt']) {
        materials['ch31_tshirt'].color = this.hexToColor(colors.shirtColor);
console.log('   → Applied t-shirt color:', colors.shirtColor);
      }
      if (materials['ch31_sweater']) {
        materials['ch31_sweater'].color = this.hexToColor(colors.shirtColor);
console.log('   → Applied sweater color:', colors.shirtColor);
      }

      // Apply pants color from combination
      if (materials['ch31_pants']) {
        materials['ch31_pants'].color = this.hexToColor(colors.pantsColor);
console.log('   → Applied pants color:', colors.pantsColor);
      }
      if (materials['ch31_jeans']) {
        materials['ch31_jeans'].color = this.hexToColor(colors.pantsColor);
console.log('   → Applied jeans color:', colors.pantsColor);
      }

      // Apply shoes color from combination
      if (materials['ch31_shoes']) {
        materials['ch31_shoes'].color = this.hexToColor(colors.shoesColor);
console.log('   → Applied shoes color:', colors.shoesColor);
      }
      if (materials['ch31_sneakers']) {
        materials['ch31_sneakers'].color = this.hexToColor(colors.shoesColor);
console.log('   → Applied sneakers color:', colors.shoesColor);
      }

console.log('✅ Applied male model colors successfully');
    } catch (error) {
console.error('❌ Error applying male model colors:', error);
    }
  }

  // Apply colors to female model meshes with dynamic colors
  static applyFemaleModelColors(materials: any, combination: ColorCombination, modelColors?: ModelColors) {
    if (!materials) return;

    try {
      const colors = modelColors || {
        hairColor: '#4a2c17',
        shirtColor: combination.shirt,
        pantsColor: combination.pants,
        shoesColor: combination.shoes,
      };

console.log('Applying female model colors:', colors);

      // Apply hair color from user's face analysis
      if (materials['hair']) {
        materials['hair'].color = this.hexToColor(colors.hairColor);
console.log('   → Applied hair color:', colors.hairColor);
      }

      // Apply dress colors from combination - front and back different colors
      if (materials['frontdress']) {
        materials['frontdress'].color = this.hexToColor(colors.shirtColor);
console.log('   → Applied front dress color (shirt):', colors.shirtColor);
      }
      if (materials['backcloth']) {
        materials['backcloth'].color = this.hexToColor(colors.pantsColor);
console.log('   → Applied back cloth color (pants):', colors.pantsColor);
      }

      // Apply pants/leg color from combination
      if (materials['rightleg']) {
        materials['rightleg'].color = this.hexToColor(colors.pantsColor);
console.log('   → Applied right leg color:', colors.pantsColor);
      }
      if (materials['leftleg']) {
        materials['leftleg'].color = this.hexToColor(colors.pantsColor);
console.log('   → Applied left leg color:', colors.pantsColor);
      }

      // Apply shoes color from combination
      if (materials['rightlegheel']) {
        materials['rightlegheel'].color = this.hexToColor(colors.shoesColor);
console.log('   → Applied right heel color:', colors.shoesColor);
      }
      if (materials['leftlegheel']) {
        materials['leftlegheel'].color = this.hexToColor(colors.shoesColor);
console.log('   → Applied left heel color:', colors.shoesColor);
      }

      // Keep hand cloth as requested color (red)
      if (materials['righthandlelittlecloth']) {
        materials['righthandlelittlecloth'].color = this.hexToColor('#ff0000');
console.log('   → Applied right hand cloth color: red');
      }
      if (materials['lefthandlittlecloth']) {
        materials['lefthandlittlecloth'].color = this.hexToColor('#ff0000');
console.log('   → Applied left hand cloth color: red');
      }

console.log('✅ Applied female model colors successfully');
    } catch (error) {
console.error('❌ Error applying female model colors:', error);
    }
  }

  // Parse API response to color combinations
  static parseApiRecommendations(apiResponse: any): ColorCombination[] {
    try {
      if (apiResponse && apiResponse.recommendations) {
        return apiResponse.recommendations.map((rec: any, index: number) => ({
          id: rec.id || `api_${index}`,
          shirt: rec.combination?.shirt || '#3498db',
          pants: rec.combination?.pants || '#2c3e50',
          shoes: rec.combination?.shoes || '#000000',
          description: rec.description || 'AI recommended combination',
          confidence: rec.confidence || 0.8,
        }));
      }
    } catch (error) {
console.error('Error parsing API recommendations:', error);
    }

    // Return default combinations if API parsing fails
    return this.getDefaultCombinations();
  }

  // Get color name from hex
  static getColorName(hex: string): string {
    const colorNames: { [key: string]: string } = {
      '#000000': 'Black',
      '#ffffff': 'White',
      '#ff0000': 'Red',
      '#00ff00': 'Green',
      '#0000ff': 'Blue',
      '#ffff00': 'Yellow',
      '#ff00ff': 'Magenta',
      '#00ffff': 'Cyan',
      '#3498db': 'Sky Blue',
      '#2c3e50': 'Dark Blue',
      '#e74c3c': 'Red',
      '#2ecc71': 'Green',
      '#f39c12': 'Orange',
      '#9b59b6': 'Purple',
      '#1abc9c': 'Turquoise',
      '#34495e': 'Dark Gray',
      '#95a5a6': 'Light Gray',
      '#8b4513': 'Brown',
    };

    return colorNames[hex.toLowerCase()] || hex;
  }

  // Validate color combination
  static validateCombination(combination: ColorCombination): boolean {
    return !!(
      combination.shirt &&
      combination.pants &&
      combination.shoes &&
      combination.description
    );
  }
}

export default ColorService;
