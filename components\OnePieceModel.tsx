import { useGLTF } from '@react-three/drei';
import { useFrame, useThree } from '@react-three/fiber';
import { Asset } from 'expo-asset';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { MeshStandardMaterial } from 'three';
import { UserColors } from '../contexts/ColorContext';
import { useUserRecommendations } from '../hooks/useUserRecommendations';
import { ColorCombination } from '../services/colorService';
import { convertRecommendationToColorCombination } from '../utils/recommendationToColorCombination';

interface OnePieceModelProps {
  colorCombination?: ColorCombination;
  userColors?: UserColors | null;
  autoRotationSpeed?: number;
  enableAutoRotation?: boolean;
  manualRotationSensitivity?: number;
  position?: [number, number, number];
  zoom?: number; // Add zoom prop
  [key: string]: any;
}

export function OnePieceModel(props: OnePieceModelProps) {
  const {
    colorCombination,
    userColors,
    autoRotationSpeed = 0.015,
    enableAutoRotation = false,
    manualRotationSensitivity = 0.008,
    position = [0, -1.2, 0],
    zoom = 1, // Default zoom
    ...otherProps
  } = props;

  const [modelUri, setModelUri] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [materialsReady, setMaterialsReady] = useState(false);
  const groupRef = useRef<any>(null);
  const lastColorCombinationRef = useRef<string>('');

  // User recommendation integration with force refresh on mount
  const {
    hasRecommendations,
    mostRecentRecommendation,
    isLoading: recommendationsLoading
  } = useUserRecommendations(true); // Enable auto-fetch (always fresh data)

  // Determine effective color combination (user recommendation or provided)
  const getEffectiveColorCombination = useCallback((): ColorCombination | undefined => {
    // If a specific color combination is provided, use it
    if (colorCombination) {
console.log('🎨 OnePiece: Using provided color combination:', colorCombination.name);
      return colorCombination;
    }

    // If user has recommendations, use the most recent one
    if (hasRecommendations && mostRecentRecommendation) {
      const recommendedCombination = convertRecommendationToColorCombination(mostRecentRecommendation);
      if (recommendedCombination) {
console.log('🎨 OnePiece: Using user recommendation:', recommendedCombination.name);
        return recommendedCombination;
      }
    }

    // No color combination available
console.log('🎨 OnePiece: No color combination available, using defaults');
    return undefined;
  }, [colorCombination, hasRecommendations, mostRecentRecommendation]);

  const effectiveColorCombination = getEffectiveColorCombination();
  
  // Cache important meshes for instant updates
  const importantMeshesRef = useRef<Map<string, any>>(new Map());
  const isUpdatingColorsRef = useRef(false);

  // Enhanced touch interaction state with zoom support
  const [isDragging, setIsDragging] = useState(false);
  const [currentZoom, setCurrentZoom] = useState(zoom);
  const lastTouchRef = useRef<{ x: number; y: number } | null>(null);
  const lastDistanceRef = useRef(0);
  const rotationVelocityRef = useRef(0);

  const { gl } = useThree();

  // Touch handlers (unchanged)
  const handlePointerDown = useCallback((event: any) => {
    if (event.preventDefault && typeof event.preventDefault === 'function') {
      event.preventDefault();
    }

    setIsDragging(true);
    const clientX = event.clientX || (event.touches && event.touches[0]?.clientX) || 0;
    const clientY = event.clientY || (event.touches && event.touches[0]?.clientY) || 0;

    lastTouchRef.current = { x: clientX, y: clientY };
    rotationVelocityRef.current = 0;
  }, []);

  const handlePointerMove = useCallback((event: any) => {
    if (!isDragging || !lastTouchRef.current || !groupRef.current) return;

    if (event.preventDefault && typeof event.preventDefault === 'function') {
      event.preventDefault();
    }

    const clientX = event.clientX || (event.touches && event.touches[0]?.clientX) || 0;
    const clientY = event.clientY || (event.touches && event.touches[0]?.clientY) || 0;

    // ONLY HORIZONTAL (X-axis) rotation - ignore vertical movement
    const deltaX = clientX - lastTouchRef.current.x;
    // Ignore deltaY completely to prevent any vertical rotation

    // Enhanced sensitivity for smooth horizontal rotation
    const rotationAmount = (deltaX * manualRotationSensitivity * 0.15);

    // CRITICAL: Only apply X-axis rotation (horizontal)
    groupRef.current.rotation.x += rotationAmount;

    // Ensure Y and Z rotations stay at 0 (no vertical or roll rotation)
    groupRef.current.rotation.y = 0;
    groupRef.current.rotation.z = 0;

    rotationVelocityRef.current = rotationAmount;

    // Only update X position for next calculation
    lastTouchRef.current = { x: clientX, y: lastTouchRef.current.y };

console.log('🔄 OnePiece: Horizontal rotation applied:', rotationAmount, 'Total X rotation:', groupRef.current.rotation.x);
  }, [isDragging, manualRotationSensitivity]);

  const handlePointerUp = useCallback((event: any) => {
    if (event.preventDefault && typeof event.preventDefault === 'function') {
      event.preventDefault();
    }

    setIsDragging(false);
    lastTouchRef.current = null;
    rotationVelocityRef.current = 0;
  }, []);

  // Touch event setup (unchanged)
  useEffect(() => {
    const canvas = gl.domElement;

    const touchStartHandler = (e: any) => handlePointerDown(e);
    const touchMoveHandler = (e: any) => handlePointerMove(e);
    const touchEndHandler = (e: any) => handlePointerUp(e);

    canvas.addEventListener('touchstart', touchStartHandler, { passive: false });
    canvas.addEventListener('touchmove', touchMoveHandler, { passive: false });
    canvas.addEventListener('touchend', touchEndHandler, { passive: false });
    canvas.addEventListener('touchcancel', touchEndHandler, { passive: false });

    canvas.addEventListener('mousedown', touchStartHandler);
    canvas.addEventListener('mousemove', touchMoveHandler);
    canvas.addEventListener('mouseup', touchEndHandler);

    const contextMenuHandler = (e: any) => {
      if (e.preventDefault && typeof e.preventDefault === 'function') {
        e.preventDefault();
      }
    };
    canvas.addEventListener('contextmenu', contextMenuHandler);

    return () => {
      canvas.removeEventListener('touchstart', touchStartHandler);
      canvas.removeEventListener('touchmove', touchMoveHandler);
      canvas.removeEventListener('touchend', touchEndHandler);
      canvas.removeEventListener('touchcancel', touchEndHandler);
      canvas.removeEventListener('mousedown', touchStartHandler);
      canvas.removeEventListener('mousemove', touchMoveHandler);
      canvas.removeEventListener('mouseup', touchEndHandler);
      canvas.removeEventListener('contextmenu', contextMenuHandler);
    };
  }, [handlePointerDown, handlePointerMove, handlePointerUp]);

  // Model loading (unchanged)
  useEffect(() => {
    const loadModel = async () => {
      try {
        if (isLoaded) return;

console.log('🚀 OnePiece: Starting optimized model loading...');
        const startTime = performance.now();

        let uri;
        try {
          uri = require('../assets/models/onepice.glb');
          setModelUri(uri);
          setIsLoaded(true);

          const loadTime = performance.now() - startTime;
console.log(`✅ OnePiece: Model loaded in ${loadTime.toFixed(2)}ms`);
          return;
        } catch (err: any) {
console.log('Direct require failed, using Asset loading', err);
        }

        const onePieceAsset = Asset.fromModule(require('../assets/models/onepice.glb'));

        if (onePieceAsset.localUri || onePieceAsset.uri) {
          uri = onePieceAsset.localUri || onePieceAsset.uri;
          setModelUri(uri);
          setIsLoaded(true);
        }

        if (!onePieceAsset.downloaded) {
          await onePieceAsset.downloadAsync();
          const downloadedUri = onePieceAsset.localUri || onePieceAsset.uri;
          if (downloadedUri && downloadedUri !== uri) {
            setModelUri(downloadedUri);
          }
        }

        setIsLoaded(true);
        const loadTime = performance.now() - startTime;
console.log(`✅ OnePiece: Model loaded via Asset in ${loadTime.toFixed(2)}ms`);
      } catch (error) {
console.error('❌ OnePiece: Error loading model:', error);
      }
    };

    loadModel();
  }, [isLoaded]);
  
  const gltf = modelUri ? useGLTF(modelUri) : null;

  // OPTIMIZED: Cache important meshes once for instant updates
  useEffect(() => {
    if (!gltf || !gltf.scene || materialsReady) return;

console.log('🎯 OnePiece: Caching important meshes for instant updates...');
    const startTime = performance.now();

    const importantMeshPatterns = [
      'face', 'neck', 'ear', 'head',
      'handfinger', 'handwrist', 'handright', 'leftfinger', 'lefthand',
      'leftleg', 'rightleg', 'arm', 'waist',
      'frontdress', 'dressback', 'dresshandpart',
      'hair', 'footwear', 'foootwear',
      'node1.107', 'node1.010'
    ];

    const meshCache = new Map();
    let cachedMeshes = 0;

    // Cache important meshes with their color categories (SAFE mesh name handling)
    gltf.scene.traverse((child: any) => {
      if (child.isMesh && child.name) {
        // SAFE: Handle undefined/null mesh names to prevent trim errors
        const safeName = (child.name || '').toString().toLowerCase();
        const isImportant = importantMeshPatterns.some(pattern => safeName.includes(pattern));

        if (isImportant) {
          // Determine color category with safe string operations
          let colorCategory = 'dress'; // default

          if (safeName.includes('face') || safeName.includes('neck') || safeName.includes('ear') ||
              safeName.includes('handfinger') || safeName.includes('handwrist') || safeName.includes('handright') ||
              safeName.includes('leftfinger') || safeName.includes('lefthand') || safeName.includes('leftleg') ||
              safeName.includes('rightleg') || safeName.includes('waist') || safeName.includes('node1.107')) {
            colorCategory = 'body';
          } else if (safeName.includes('hair') || safeName.includes('node1.010')) {
            colorCategory = 'hair';
          } else if (safeName.includes('frontdress') || safeName.includes('dressback') || safeName.includes('dresshandpart')) {
            colorCategory = 'dress';
          } else if (safeName.includes('footwear') || safeName.includes('foootwear')) {
            colorCategory = 'footwear';
          }

          // Create optimized material if needed
          if (!child.material || !child.material.color) {
            child.material = new MeshStandardMaterial({
              color: '#ffffff',
              roughness: 0.6,
              metalness: 0.05,
              transparent: false,
              fog: false,
              toneMapped: false,
            });
          }

          // Cache mesh with metadata
          meshCache.set(child.uuid, {
            mesh: child,
            colorCategory,
            name: child.name
          });

          // Performance optimizations
          child.castShadow = false;
          child.receiveShadow = false;
          child.frustumCulled = true;
          child.matrixAutoUpdate = false;

          cachedMeshes++;
        }
      }
    });

    importantMeshesRef.current = meshCache;
    setMaterialsReady(true);
    
    const setupTime = performance.now() - startTime;
console.log(`🎯 OnePiece: Cached ${cachedMeshes} important meshes in ${setupTime.toFixed(2)}ms`);
  }, [gltf]);

  // INSTANT COLOR UPDATE: Use cached meshes for immediate updates
  useEffect(() => {
    if (!materialsReady || importantMeshesRef.current.size === 0) return;
    if (isUpdatingColorsRef.current) return; // Prevent concurrent updates

    const currentColorKey = `${colorCombination?.shirt || '#e91e63'}-${colorCombination?.shoes || '#000000'}`;
    
    if (lastColorCombinationRef.current === currentColorKey) {
      return; // No change needed
    }

console.log('🎯 OnePiece: INSTANT color update starting...');
    const startTime = performance.now();
    
    isUpdatingColorsRef.current = true;

    // Use effective color combination (user recommendation or provided)
    const activeColorCombination = effectiveColorCombination;

    // Color mapping for instant lookup
    const colorMap = {
      body: '#fdbcb4',
      hair: '#000000',
      dress: activeColorCombination?.shirt || '#e91e63',
      footwear: activeColorCombination?.shoes || '#000000'
    };

    // Log which color source is being used
    if (activeColorCombination?.isRecommended) {
console.log('🎨 OnePiece: Applying user recommended colors:', {
        shirt: activeColorCombination.shirt,
        shoes: activeColorCombination.shoes
      });
    } else if (activeColorCombination) {
console.log('🎨 OnePiece: Applying provided colors:', {
        shirt: activeColorCombination.shirt,
        shoes: activeColorCombination.shoes
      });
    } else {
console.log('🎨 OnePiece: Applying default colors');
    }

    let updatedMeshes = 0;

    // INSTANT UPDATE: Direct color application to cached meshes
    try {
      importantMeshesRef.current.forEach((meshData) => {
        const { mesh, colorCategory } = meshData;
        const targetColor = colorMap[colorCategory as keyof typeof colorMap];

        if (mesh && mesh.material && targetColor) {
          // AGGRESSIVE: Force immediate visual update
          if (Array.isArray(mesh.material)) {
            mesh.material.forEach((mat: any) => {
              if (mat && mat.color) {
                mat.color.set(targetColor);
                mat.needsUpdate = true;
                // FORCE immediate rendering
                mat.uniformsNeedUpdate = true;
                if (mat.map) mat.map.needsUpdate = true;
              }
            });
          } else if (mesh.material.color) {
            mesh.material.color.set(targetColor);
            mesh.material.needsUpdate = true;
            // FORCE immediate rendering
            mesh.material.uniformsNeedUpdate = true;
            if (mesh.material.map) mesh.material.map.needsUpdate = true;
          }

          // CRITICAL: Force mesh updates for immediate visual change
          if (mesh.geometry && mesh.geometry.attributes.position) {
            mesh.geometry.attributes.position.needsUpdate = true;
          }
          mesh.updateMatrix();
          mesh.updateMatrixWorld(true);

          updatedMeshes++;
        }
      });

      // AGGRESSIVE: Force complete scene update for immediate visual change
      if (gltf && gltf.scene) {
        gltf.scene.updateMatrixWorld(true);
        if (gltf.scene.parent) {
          gltf.scene.parent.updateMatrixWorld(true);
        }
      }

      lastColorCombinationRef.current = currentColorKey;

      const updateTime = performance.now() - startTime;
console.log(`✅ OnePiece: INSTANT update completed in ${updateTime.toFixed(2)}ms`);
console.log(`🎯 OnePiece: Updated ${updatedMeshes} cached meshes instantly`);

      // CRITICAL: Force multiple render frames for immediate visual update
      let frameCount = 0;
      const forceVisualUpdate = () => {
        frameCount++;
        if (frameCount < 3) { // Force 3 immediate frames
          requestAnimationFrame(forceVisualUpdate);
        }
console.log(`🚀 OnePiece: Forced visual update frame ${frameCount}`);
      };

      if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(forceVisualUpdate);
      }

      // CRITICAL: Trigger useFrame force updates for 10 frames
      forceUpdateFramesRef.current = 10;
console.log(`🚀 OnePiece: Triggering 10 frames of forced material updates`);

    } catch (error) {
console.error('❌ OnePiece: Error in instant color update:', error);
    } finally {
      isUpdatingColorsRef.current = false;
    }

  }, [effectiveColorCombination, materialsReady]);

  // Force visual updates and rotation
  const forceUpdateFramesRef = useRef(0);

  useFrame((_state, delta) => {
    // Ensure model stays in correct orientation (no unwanted rotations)
    if (groupRef.current) {
      // CRITICAL: Lock Y and Z rotations to prevent vertical/roll rotation
      groupRef.current.rotation.y = 0;
      groupRef.current.rotation.z = 0;

      // Auto-rotation (disabled by default)
      if (enableAutoRotation && !isDragging) {
        const rotationSpeed = autoRotationSpeed * delta * 60;
        groupRef.current.rotation.x += rotationSpeed;
      }
    }

    // CRITICAL: Force material updates for immediate visual changes
    if (forceUpdateFramesRef.current > 0) {
      forceUpdateFramesRef.current--;

      // Force all cached mesh materials to update
      importantMeshesRef.current.forEach((meshData) => {
        const { mesh } = meshData;
        if (mesh && mesh.material) {
          if (Array.isArray(mesh.material)) {
            mesh.material.forEach((mat: any) => {
              if (mat) {
                mat.needsUpdate = true;
                mat.uniformsNeedUpdate = true;
              }
            });
          } else {
            mesh.material.needsUpdate = true;
            mesh.material.uniformsNeedUpdate = true;
          }
        }
      });

console.log(`🚀 OnePiece: Forcing material updates - ${forceUpdateFramesRef.current} frames remaining`);
    }
  });
  
  // Only render when ready
  if (!gltf || !gltf.scene || !materialsReady) {
    return null;
  }

  // Apply zoom to scale (use currentZoom for dynamic updates)
  const zoomedScale: [number, number, number] = [currentZoom, currentZoom, currentZoom];

  return (
    <group
      ref={groupRef}
      position={position}
      scale={zoomedScale} // Apply zoom to scale
      rotation={[0, 0, 0]} // CRITICAL: Start with no rotation
      {...otherProps}
      dispose={null}
    >
      <primitive
        object={gltf.scene}
        frustumCulled={true}
        renderOrder={0}
        position={[0, 0, 0]}
        rotation={[0, 0, 0]} // CRITICAL: Ensure primitive also starts with no rotation
      />
    </group>
  );
}

// Preloading
console.log('🚀 OnePiece: Starting model preload...');
try {
  useGLTF.preload(require('../assets/models/onepice.glb'));
console.log('✅ OnePiece: Model preloaded successfully');
} catch (error) {
console.error('❌ OnePiece: Preload failed:', error);
}